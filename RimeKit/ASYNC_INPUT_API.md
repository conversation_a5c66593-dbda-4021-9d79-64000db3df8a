# RimeKit 输入API使用指南

## 概述

RimeKit的`inputKey`方法采用异步设计，参考Android端的实现模式，为iOS端提供了高性能的异步输入处理能力。异步API可以避免阻塞主线程，提供更流畅的用户体验。

## API接口

### 1. 统一回调方式

```objc
- (void)inputKey:(RimeKeyboardEvent *)keyEvent
        callback:(RimeInputKeyCallback)callback;
```

**参数说明：**
- `keyEvent`: 按键事件对象
- `callback`: 统一回调块，包含候选词、组合状态和错误信息

**回调块定义：**
```objc
typedef void (^RimeInputKeyCallback)(RimeCandidateList * _Nullable candidates, 
                                   RimeComposition * _Nullable composition, 
                                   NSString * _Nullable error);
```

### 2. 分离回调方式

```objc
- (void)inputKey:(RimeKeyboardEvent *)keyEvent
         success:(RimeInputKeySuccessCallback)successCallback
           error:(RimeInputKeyErrorCallback)errorCallback;
```

**参数说明：**
- `keyEvent`: 按键事件对象
- `successCallback`: 成功回调块
- `errorCallback`: 错误回调块

**回调块定义：**
```objc
typedef void (^RimeInputKeySuccessCallback)(RimeCandidateList * _Nullable candidates, 
                                          RimeComposition * _Nullable composition);
typedef void (^RimeInputKeyErrorCallback)(NSString *error);
```

## 使用示例

### 基础使用

```objc
// 创建按键事件
RimeKeyboardEvent *keyEvent = [[RimeKeyboardEvent alloc] initWithKeyCode:'a' modifiers:0];

// 异步处理
[session inputKey:keyEvent callback:^(RimeCandidateList * _Nullable candidates,
                                     RimeComposition * _Nullable composition,
                                     NSString * _Nullable error) {
    if (error) {
        NSLog(@"处理失败: %@", error);
    } else {
        NSLog(@"处理成功");
        if (candidates) {
            NSLog(@"候选词数量: %lu", (unsigned long)candidates.count);
        }
        if (composition) {
            NSLog(@"当前输入: %@", composition.preedit);
        }
    }
}];
```

### 分离回调使用

```objc
[session inputKey:keyEvent
          success:^(RimeCandidateList * _Nullable candidates,
                   RimeComposition * _Nullable composition) {
                   // 处理成功
                   NSLog(@"输入处理成功");
                   if (candidates) {
                       for (RimeCandidate *candidate in candidates.candidates) {
                           NSLog(@"候选词: %@", candidate.text);
                       }
                   }
               }
                 error:^(NSString *error) {
                   // 处理错误
                   NSLog(@"输入处理失败: %@", error);
               }];
```

## 技术实现

### 线程模型

- **处理线程**: 使用串行队列`com.rimekit.inputsession.processing`进行按键处理，确保按键处理的顺序性
- **回调线程**: 所有回调都在主线程中执行，方便UI更新
- **委托更新**: 异步处理完成后会自动更新`RimeInputSessionDelegate`

### 性能优势

1. **非阻塞**: 按键处理在后台线程进行，不会阻塞主线程
2. **顺序保证**: 使用串行队列确保按键处理的顺序性
3. **内存安全**: 自动处理异常情况，避免崩溃
4. **向后兼容**: 保留原有的同步API，不影响现有代码

### 错误处理

异步API会捕获以下错误：
- 会话无效或已关闭
- 按键处理失败
- 引擎内部异常

所有错误都会通过回调返回，不会抛出异常。

## 最佳实践

1. **优先使用异步API**: 对于频繁的按键输入，建议使用异步API以获得更好的性能
2. **合理处理错误**: 始终检查错误参数，并提供适当的错误处理
3. **避免嵌套过深**: 对于连续的按键处理，考虑使用队列或状态机模式
4. **内存管理**: 注意回调块中的循环引用问题，使用`__weak`引用

## 与Android端对比

| 特性 | Android | iOS |
|------|---------|-----|
| 回调接口 | `InputKeyCallback` | `RimeInputKeyCallback` |
| 线程模型 | `ExecutorService` + `Handler` | `dispatch_queue` + `dispatch_async` |
| 错误处理 | 异常捕获 | `@try/@catch` + 回调 |
| 委托更新 | 手动调用 | 自动更新 |

## 注意事项

1. 异步API的回调总是在主线程中执行
2. 如果会话已关闭，异步调用会立即返回错误
3. 委托方法会在异步处理完成后自动调用
4. 建议在UI密集操作时使用异步API以保持流畅性
