//
//  SimpleRimeViewController.m
//  RimeKit Example
//
//  极简版 RimeKit 使用示例
//

#import <UIKit/UIKit.h>
#import <RimeKit/RimeKit.h>

@interface SimpleRimeViewController : UIViewController <RimeManagerDelegate>

@property (nonatomic, strong) UITextField *inputField;
@property (nonatomic, strong) UILabel *candidatesLabel;
@property (nonatomic, strong) RimeManager *rimeManager;

@end

@implementation SimpleRimeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    [self setupRime];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];
    
    // 输入框
    self.inputField = [[UITextField alloc] initWithFrame:CGRectMake(20, 100, 300, 40)];
    self.inputField.borderStyle = UITextBorderStyleRoundedRect;
    self.inputField.placeholder = @"请输入拼音";
    [self.inputField addTarget:self action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
    [self.view addSubview:self.inputField];
    
    // 候选词显示
    self.candidatesLabel = [[UILabel alloc] initWithFrame:CGRectMake(20, 160, 300, 100)];
    self.candidatesLabel.numberOfLines = 0;
    self.candidatesLabel.backgroundColor = [UIColor lightGrayColor];
    [self.view addSubview:self.candidatesLabel];
    
    // 翻页按钮
    UIButton *pageUpButton = [UIButton buttonWithType:UIButtonTypeSystem];
    pageUpButton.frame = CGRectMake(20, 280, 100, 40);
    [pageUpButton setTitle:@"上一页" forState:UIControlStateNormal];
    [pageUpButton addTarget:self action:@selector(pageUp) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:pageUpButton];
    
    UIButton *pageDownButton = [UIButton buttonWithType:UIButtonTypeSystem];
    pageDownButton.frame = CGRectMake(140, 280, 100, 40);
    [pageDownButton setTitle:@"下一页" forState:UIControlStateNormal];
    [pageDownButton addTarget:self action:@selector(pageDown) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:pageDownButton];
    
    // 重置按钮
    UIButton *resetButton = [UIButton buttonWithType:UIButtonTypeSystem];
    resetButton.frame = CGRectMake(260, 280, 60, 40);
    [resetButton setTitle:@"重置" forState:UIControlStateNormal];
    [resetButton addTarget:self action:@selector(reset) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:resetButton];
}

- (void)setupRime {
    // 极简初始化 - 仅需3行代码
    self.rimeManager = [RimeManager sharedManager];
    [self.rimeManager initializeEngine];
    [self.rimeManager setImeCallback:self];
}

#pragma mark - Actions

- (void)textFieldDidChange:(UITextField *)textField {
    NSString *text = textField.text;
    if (text.length == 0) {
        [self.rimeManager reset];
        return;
    }

    // 处理最后一个字符
    char lastChar = [text characterAtIndex:text.length - 1];
    RimeKeyboardEvent *event = [RimeKeyboardEvent fromCharacter:lastChar];

    // 使用异步API处理输入
    [self.rimeManager inputKey:event callback:^(RimeCandidateList * _Nullable candidates, RimeComposition * _Nullable composition, NSString * _Nullable error) {
        if (error) {
            NSLog(@"输入处理失败: %@", error);
        } else {
            // 在主线程中更新UI
            [self updateCandidatesDisplay:candidates];
        }
    }];
}

- (void)pageUp {
    RimeCandidateList *candidates = [self.rimeManager pageUp];
    [self updateCandidatesDisplay:candidates];
}

- (void)pageDown {
    RimeCandidateList *candidates = [self.rimeManager pageDown];
    [self updateCandidatesDisplay:candidates];
}

- (void)reset {
    [self.rimeManager reset];
    self.inputField.text = @"";
    self.candidatesLabel.text = @"";
}

#pragma mark - RimeManagerDelegate

- (void)onUpdateCandidateWords:(RimeCandidateList *)candidateList {
    // 异步候选词更新回调
    [self updateCandidatesDisplay:candidateList];
}

#pragma mark - Private

- (void)updateCandidatesDisplay:(RimeCandidateList *)candidateList {
    if (!candidateList || candidateList.isEmpty) {
        self.candidatesLabel.text = @"无候选词";
        return;
    }
    
    NSMutableString *display = [NSMutableString string];
    for (NSInteger i = 0; i < candidateList.count; i++) {
        NSString *candidate = [candidateList textAtIndex:i];
        NSString *label = [candidateList labelAtIndex:i];
        [display appendFormat:@"%@. %@  ", label, candidate];
        if ((i + 1) % 3 == 0) {
            [display appendString:@"\n"];
        }
    }
    
    self.candidatesLabel.text = display;
}

@end