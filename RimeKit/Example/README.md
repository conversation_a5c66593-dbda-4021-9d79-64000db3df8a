# RimeKit 使用示例

本目录包含 RimeKit 的基础使用示例，展示如何在 Objective-C 和 Swift 中使用 RimeKit 进行中文输入法功能。

## 文件说明

### RimeKitExample.m
- **语言**: Objective-C
- **描述**: 完整的 RimeKit 使用示例，包含初始化、基础输入、高级功能和资源管理
- **运行方式**: 作为命令行工具或集成到 iOS 项目中

### RimeKitSwiftExample.swift
- **语言**: Swift
- **描述**: Swift 版本的 RimeKit 使用示例，功能与 Objective-C 版本相同
- **运行方式**: 作为 Swift 脚本或集成到 iOS 项目中

## 主要功能演示

### 1. 初始化和配置
- RimeKit 框架初始化
- 输入会话创建
- 输入方案选择
- 可用方案列表显示

### 2. 基础输入功能
- 拼音输入演示
- 候选词显示
- 文本提交
- 输入状态监控

### 3. 高级功能
- 候选词选择
- 翻页功能
- 输入控制（设置输入串、光标位置）
- 状态监控

### 4. 资源管理
- 维护任务执行
- 资源清理
- 状态检查

## 使用方法

### 在 iOS 项目中使用

1. 将 RimeKit 框架添加到项目中
2. 导入必要的头文件或模块
3. 参考示例代码进行集成

```objc
// Objective-C
#import <RimeKit/RimeKit.h>

// 初始化
RimeInputManager *manager = [RimeInputManager sharedManager];
[manager initialize];

// 创建会话
RimeInputSession *session = [manager createSession];
[session selectSchema:@"rime_ice"];
```

```swift
// Swift
import RimeKit

// 初始化
let manager = RimeInputManager.shared()
manager.initialize()

// 创建会话
let session = manager.createSession()
session?.selectSchema("rime_ice")
```

### 作为命令行工具运行

```bash
# 编译 Objective-C 版本
clang -framework Foundation -framework RimeKit RimeKitExample.m -o RimeKitExample

# 运行
./RimeKitExample
```

## 注意事项

1. **资源文件**: 确保 RimeKit 资源文件（词典、配置文件等）正确部署
2. **权限配置**: 在 iOS 项目中可能需要配置相关权限
3. **内存管理**: 使用完毕后及时清理资源
4. **错误处理**: 实际使用时应添加完整的错误处理逻辑

## 输入方案

示例中使用的输入方案：
- **rime_ice**: 拼音输入方案
- **t9**: T9 数字键盘输入方案

可通过 `schemaList` 方法获取所有可用方案。

## 扩展功能

基于示例代码，你可以扩展以下功能：
- 自定义候选词过滤
- 输入历史记录
- 用户词典管理
- 个性化设置
- 云同步功能