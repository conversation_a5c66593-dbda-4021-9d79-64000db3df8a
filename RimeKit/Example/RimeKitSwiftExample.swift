//
//  RimeKitSwiftExample.swift
//  RimeKit Swift 使用示例
//
//  演示如何在 Swift 中使用 RimeKit 进行中文输入法功能
//

import Foundation
import RimeKit

class RimeKitSwiftExample {
    
    private var inputManager: RimeInputManager?
    private var inputSession: RimeInputSession?
    
    init() {
        setupRimeKit()
    }
    
    // MARK: - RimeKit 初始化
    
    private func setupRimeKit() {
        print("=== RimeKit 初始化开始 ===")
        
        // 1. 获取 RimeInputManager 单例
        inputManager = RimeInputManager.shared()
        
        // 2. 初始化 RimeKit
        guard let manager = inputManager, manager.initialize() else {
            print("❌ RimeKit 初始化失败")
            return
        }
        
        print("✅ RimeKit 初始化成功")
        print("📱 RimeKit 版本: \(manager.version())")
        
        // 3. 创建输入会话
        inputSession = manager.createSession()
        guard let session = inputSession else {
            print("❌ 创建输入会话失败")
            return
        }
        
        print("✅ 输入会话创建成功")
        
        // 4. 显示可用的输入方案
        displayAvailableSchemas()
        
        // 5. 选择默认输入方案
        if session.selectSchema("rime_ice") {
            print("✅ 选择拼音输入方案成功")
        } else {
            print("⚠️ 选择拼音输入方案失败，尝试其他方案")
            selectAlternativeSchema()
        }
    }
    
    private func displayAvailableSchemas() {
        print("\n=== 可用输入方案 ===")
        
        guard let schemas = inputManager?.schemaList() as? [[String: Any]] else {
            print("⚠️ 没有找到可用的输入方案")
            return
        }
        
        for (index, schema) in schemas.enumerated() {
            let name = schema["name"] as? String ?? "未知"
            let schemaId = schema["schema_id"] as? String ?? "未知ID"
            print("\(index + 1). \(name) (\(schemaId))")
        }
    }
    
    private func selectAlternativeSchema() {
        guard let schemas = inputManager?.schemaList() as? [[String: Any]],
              !schemas.isEmpty,
              let firstSchema = schemas.first,
              let schemaId = firstSchema["schema_id"] as? String else {
            print("❌ 没有可用的备选方案")
            return
        }
        
        if inputSession?.selectSchema(schemaId) == true {
            print("✅ 选择方案 \(schemaId) 成功")
        } else {
            print("❌ 选择备选方案失败")
        }
    }
    
    // MARK: - 基础输入演示
    
    func demonstrateBasicInput() {
        print("\n=== 基础输入演示 ===")
        
        // 演示输入 "ni hao"
        simulateInput("ni hao")
        
        // 演示输入 "zhong guo"
        simulateInput("zhong guo")
        
        // 演示输入 "han yu"
        simulateInput("han yu")
    }
    
    private func simulateInput(_ inputText: String) {
        print("\n--- 输入: \(inputText) ---")
        
        guard let session = inputSession else {
            print("❌ 输入会话不可用")
            return
        }
        
        // 清除之前的输入
        session.clearComposition()
        
        // 逐字符输入
        for character in inputText {
            if character == " " {
                // 空格键 - 选择第一个候选词
                if session.processKey(Int32(character.asciiValue!), mask: 0) {
                    print("💬 空格键处理成功")
                }
            } else {
                // 字符输入
                if session.processKey(Int32(character.asciiValue!), mask: 0) {
                    print("⌨️ 输入字符: \(character)")
                }
            }
            
            // 显示当前状态
            displayCurrentState()
        }
        
        // 如果还有未提交的内容，提交它
        commitCurrentInput()
    }
    
    private func displayCurrentState() {
        guard let session = inputSession else { return }
        
        // 显示当前组合
        if let composition = session.composition(),
           !composition.preedit.isEmpty {
            print("📝 当前组合: \(composition.preedit)")
        }
        
        // 显示候选词
        if let candidates = session.candidates(),
           candidates.count > 0 {
            print("📋 候选词:")
            for i in 0..<min(candidates.count, 5) {
                if let candidate = candidates.candidate(at: i) {
                    print("  \(i + 1). \(candidate.text)")
                }
            }
        }
        
        // 显示提交的文本
        if let commit = session.commit(),
           !commit.text.isEmpty {
            print("✍️ 提交文本: \(commit.text)")
        }
    }
    
    private func commitCurrentInput() {
        guard let session = inputSession else { return }
        
        if session.hasComposition() {
            // 提交当前组合
            session.commitComposition()
            
            // 获取提交的文本
            if let commit = session.commit(),
               !commit.text.isEmpty {
                print("✅ 最终提交: \(commit.text)")
            }
        }
    }
    
    // MARK: - 高级功能演示
    
    func demonstrateAdvancedFeatures() {
        print("\n=== 高级功能演示 ===")
        
        // 演示候选词选择
        demonstrateCandidateSelection()
        
        // 演示翻页功能
        demonstratePageNavigation()
        
        // 演示输入控制
        demonstrateInputControl()
    }
    
    private func demonstrateCandidateSelection() {
        print("\n--- 候选词选择演示 ---")
        
        guard let session = inputSession else { return }
        
        // 输入拼音
        session.clearComposition()
        let pinyin = "wo"
        
        for character in pinyin {
            session.processKey(Int32(character.asciiValue!), mask: 0)
        }
        
        // 显示候选词
        if let candidates = session.candidates(),
           candidates.count > 0 {
            print("📋 可选候选词:")
            for i in 0..<min(candidates.count, 3) {
                if let candidate = candidates.candidate(at: i) {
                    print("  \(i + 1). \(candidate.text)")
                }
            }
            
            // 选择第二个候选词
            if candidates.count > 1 {
                session.selectCandidate(1)
                if let commit = session.commit() {
                    print("✅ 选择第2个候选词: \(commit.text)")
                }
            }
        }
    }
    
    private func demonstratePageNavigation() {
        print("\n--- 翻页功能演示 ---")
        
        guard let session = inputSession else { return }
        
        // 输入一个会产生很多候选词的拼音
        session.clearComposition()
        let pinyin = "zhang"
        
        for character in pinyin {
            session.processKey(Int32(character.asciiValue!), mask: 0)
        }
        
        // 显示第一页候选词
        if let candidates = session.candidates(),
           candidates.count > 0 {
            print("📋 第一页候选词:")
            for i in 0..<min(candidates.count, 5) {
                if let candidate = candidates.candidate(at: i) {
                    print("  \(i + 1). \(candidate.text)")
                }
            }
            
            // 尝试翻页
            if session.processKey(0xFF54, mask: 0) { // Page Down
                print("📄 翻到下一页")
                displayCurrentState()
            }
        }
        
        // 清理
        session.clearComposition()
    }
    
    private func demonstrateInputControl() {
        print("\n--- 输入控制演示 ---")
        
        guard let session = inputSession else { return }
        
        // 设置输入串
        session.setInput("beijing")
        print("📝 设置输入串: \(session.input() ?? "")")
        
        // 设置光标位置
        session.setCaretPosition(3)
        print("📍 设置光标位置: \(session.caretPosition())")
        
        // 显示当前状态
        displayCurrentState()
        
        // 清理
        session.clearComposition()
    }
    
    // MARK: - 状态监控
    
    func demonstrateStatusMonitoring() {
        print("\n=== 状态监控演示 ===")
        
        guard let session = inputSession else { return }
        
        // 获取引擎状态
        if let status = session.status() {
            print("📊 引擎状态:")
            print("  当前方案: \(status.schemaName)")
            print("  是否启用: \(status.isEnabled ? "是" : "否")")
            print("  是否可用: \(status.isAvailable ? "是" : "否")")
        }
        
        // 监控输入过程
        monitorInputProcess("hello")
    }
    
    private func monitorInputProcess(_ input: String) {
        print("\n--- 监控输入过程: \(input) ---")
        
        guard let session = inputSession else { return }
        
        session.clearComposition()
        
        for character in input {
            print("⌨️ 输入: \(character)")
            session.processKey(Int32(character.asciiValue!), mask: 0)
            
            // 监控状态变化
            if let context = session.context() {
                print("📊 上下文状态:")
                if let composition = context.composition {
                    print("  组合: \(composition.preedit)")
                }
                if let menu = context.menu,
                   menu.candidates.count > 0 {
                    print("  候选词数: \(menu.candidates.count)")
                }
            }
        }
    }
    
    // MARK: - 资源管理
    
    func demonstrateResourceManagement() {
        print("\n=== 资源管理演示 ===")
        
        guard let manager = inputManager else { return }
        
        // 检查维护状态
        if manager.isMaintenancing() {
            print("⚠️ 系统正在维护中...")
        } else {
            print("✅ 系统运行正常")
        }
        
        // 执行维护任务
        if manager.performMaintenance() {
            print("🔧 维护任务启动成功")
        } else {
            print("❌ 维护任务启动失败")
        }
    }
    
    func cleanup() {
        print("\n=== 清理资源 ===")
        
        // 清理输入会话
        if let session = inputSession {
            session.clearComposition()
            inputSession = nil
            print("✅ 输入会话已清理")
        }
        
        // 销毁输入管理器
        if let manager = inputManager {
            manager.destroy()
            inputManager = nil
            print("✅ 输入管理器已销毁")
        }
    }
}

// MARK: - 主函数演示

func main() {
    print("🚀 RimeKit Swift 使用示例开始")
    
    // 创建示例实例
    let example = RimeKitSwiftExample()
    
    // 演示基础输入
    example.demonstrateBasicInput()
    
    // 演示高级功能
    example.demonstrateAdvancedFeatures()
    
    // 演示状态监控
    example.demonstrateStatusMonitoring()
    
    // 演示资源管理
    example.demonstrateResourceManagement()
    
    // 清理资源
    example.cleanup()
    
    print("🎉 RimeKit Swift 使用示例结束")
}

// 运行示例
main()