//
//  RimeKitExample.m
//  RimeKit 基础使用示例
//
//  演示如何使用 RimeKit 进行中文输入法功能
//

#import <Foundation/Foundation.h>
#import <RimeKit/RimeKit.h>

@interface RimeKitExample : NSObject

@property (nonatomic, strong) RimeInputManager *inputManager;
@property (nonatomic, strong) RimeInputSession *inputSession;

@end

@implementation RimeKitExample

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupRimeKit];
    }
    return self;
}

#pragma mark - RimeKit 初始化

- (BOOL)setupRimeKit {
    NSLog(@"=== RimeKit 初始化开始 ===");
    
    // 1. 获取 RimeInputManager 单例
    self.inputManager = [RimeInputManager sharedManager];
    
    // 2. 初始化 RimeKit
    if (![self.inputManager initialize]) {
        NSLog(@"❌ RimeKit 初始化失败");
        return NO;
    }
    
    NSLog(@"✅ RimeKit 初始化成功");
    NSLog(@"📱 RimeKit 版本: %@", [self.inputManager version]);
    
    // 3. 创建输入会话
    self.inputSession = [self.inputManager createSession];
    if (!self.inputSession) {
        NSLog(@"❌ 创建输入会话失败");
        return NO;
    }
    
    NSLog(@"✅ 输入会话创建成功");
    
    // 4. 显示可用的输入方案
    [self displayAvailableSchemas];
    
    // 5. 选择默认输入方案
    if ([self.inputSession selectSchema:@"rime_ice"]) {
        NSLog(@"✅ 选择拼音输入方案成功");
    } else {
        NSLog(@"⚠️ 选择拼音输入方案失败，尝试其他方案");
        // 尝试选择其他方案
        [self selectAlternativeSchema];
    }
    
    return YES;
}

- (void)displayAvailableSchemas {
    NSLog(@"\n=== 可用输入方案 ===");
    NSArray *schemas = [self.inputManager schemaList];
    if (schemas && schemas.count > 0) {
        for (int i = 0; i < schemas.count; i++) {
            NSDictionary *schema = schemas[i];
            NSLog(@"%d. %@ (%@)", i + 1, 
                  schema[@"name"] ?: @"未知", 
                  schema[@"schema_id"] ?: @"未知ID");
        }
    } else {
        NSLog(@"⚠️ 没有找到可用的输入方案");
    }
}

- (void)selectAlternativeSchema {
    NSArray *schemas = [self.inputManager schemaList];
    if (schemas && schemas.count > 0) {
        NSDictionary *firstSchema = schemas[0];
        NSString *schemaId = firstSchema[@"schema_id"];
        if (schemaId && [self.inputSession selectSchema:schemaId]) {
            NSLog(@"✅ 选择方案 %@ 成功", schemaId);
        } else {
            NSLog(@"❌ 选择备选方案失败");
        }
    }
}

#pragma mark - 基础输入演示

- (void)demonstrateBasicInput {
    NSLog(@"\n=== 基础输入演示 ===");
    
    // 演示输入 "ni hao"
    [self simulateInput:@"ni hao"];
    
    // 演示输入 "zhong guo"
    [self simulateInput:@"zhong guo"];
    
    // 演示输入 "han yu"
    [self simulateInput:@"han yu"];
}

- (void)simulateInput:(NSString *)inputText {
    NSLog(@"\n--- 输入: %@ ---", inputText);
    
    // 清除之前的输入
    [self.inputSession clearComposition];
    
    // 逐字符输入
    for (int i = 0; i < inputText.length; i++) {
        unichar character = [inputText characterAtIndex:i];
        
        if (character == ' ') {
            // 空格键 - 选择第一个候选词
            if ([self.inputSession processKey:' ' mask:0]) {
                NSLog(@"💬 空格键处理成功");
            }
        } else {
            // 字符输入
            if ([self.inputSession processKey:character mask:0]) {
                NSLog(@"⌨️ 输入字符: %c", character);
            }
        }
        
        // 显示当前状态
        [self displayCurrentState];
    }
    
    // 如果还有未提交的内容，提交它
    [self commitCurrentInput];
}

- (void)displayCurrentState {
    // 显示当前组合
    RimeComposition *composition = [self.inputSession composition];
    if (composition && composition.preedit.length > 0) {
        NSLog(@"📝 当前组合: %@", composition.preedit);
    }
    
    // 显示候选词
    RimeCandidateList *candidates = [self.inputSession candidates];
    if (candidates && candidates.count > 0) {
        NSLog(@"📋 候选词:");
        for (int i = 0; i < MIN(candidates.count, 5); i++) {
            RimeCandidate *candidate = [candidates candidateAtIndex:i];
            NSLog(@"  %d. %@", i + 1, candidate.text);
        }
    }
    
    // 显示提交的文本
    RimeCommit *commit = [self.inputSession commit];
    if (commit && commit.text.length > 0) {
        NSLog(@"✍️ 提交文本: %@", commit.text);
    }
}

- (void)commitCurrentInput {
    if ([self.inputSession hasComposition]) {
        // 提交当前组合
        [self.inputSession commitComposition];
        
        // 获取提交的文本
        RimeCommit *commit = [self.inputSession commit];
        if (commit && commit.text.length > 0) {
            NSLog(@"✅ 最终提交: %@", commit.text);
        }
    }
}

#pragma mark - 高级功能演示

- (void)demonstrateAdvancedFeatures {
    NSLog(@"\n=== 高级功能演示 ===");
    
    // 演示候选词选择
    [self demonstrateCandidateSelection];
    
    // 演示翻页功能
    [self demonstratePageNavigation];
    
    // 演示输入控制
    [self demonstrateInputControl];
}

- (void)demonstrateCandidateSelection {
    NSLog(@"\n--- 候选词选择演示 ---");
    
    // 输入拼音
    [self.inputSession clearComposition];
    NSString *pinyin = @"wo";
    
    for (int i = 0; i < pinyin.length; i++) {
        unichar character = [pinyin characterAtIndex:i];
        [self.inputSession processKey:character mask:0];
    }
    
    // 显示候选词
    RimeCandidateList *candidates = [self.inputSession candidates];
    if (candidates && candidates.count > 0) {
        NSLog(@"📋 可选候选词:");
        for (int i = 0; i < MIN(candidates.count, 3); i++) {
            RimeCandidate *candidate = [candidates candidateAtIndex:i];
            NSLog(@"  %d. %@", i + 1, candidate.text);
        }
        
        // 选择第二个候选词
        if (candidates.count > 1) {
            [self.inputSession selectCandidate:1];
            RimeCommit *commit = [self.inputSession commit];
            if (commit) {
                NSLog(@"✅ 选择第2个候选词: %@", commit.text);
            }
        }
    }
}

- (void)demonstratePageNavigation {
    NSLog(@"\n--- 翻页功能演示 ---");
    
    // 输入一个会产生很多候选词的拼音
    [self.inputSession clearComposition];
    NSString *pinyin = @"zhang";
    
    for (int i = 0; i < pinyin.length; i++) {
        unichar character = [pinyin characterAtIndex:i];
        [self.inputSession processKey:character mask:0];
    }
    
    // 显示第一页候选词
    RimeCandidateList *candidates = [self.inputSession candidates];
    if (candidates && candidates.count > 0) {
        NSLog(@"📋 第一页候选词:");
        for (int i = 0; i < MIN(candidates.count, 5); i++) {
            RimeCandidate *candidate = [candidates candidateAtIndex:i];
            NSLog(@"  %d. %@", i + 1, candidate.text);
        }
        
        // 尝试翻页
        if ([self.inputSession processKey:0xFF54 mask:0]) { // Page Down
            NSLog(@"📄 翻到下一页");
            [self displayCurrentState];
        }
    }
    
    // 清理
    [self.inputSession clearComposition];
}

- (void)demonstrateInputControl {
    NSLog(@"\n--- 输入控制演示 ---");
    
    // 设置输入串
    [self.inputSession setInput:@"beijing"];
    NSLog(@"📝 设置输入串: %@", [self.inputSession input]);
    
    // 设置光标位置
    [self.inputSession setCaretPosition:3];
    NSLog(@"📍 设置光标位置: %ld", (long)[self.inputSession caretPosition]);
    
    // 显示当前状态
    [self displayCurrentState];
    
    // 清理
    [self.inputSession clearComposition];
}

#pragma mark - 状态监控

- (void)demonstrateStatusMonitoring {
    NSLog(@"\n=== 状态监控演示 ===");
    
    // 获取引擎状态
    RimeStatus *status = [self.inputSession status];
    if (status) {
        NSLog(@"📊 引擎状态:");
        NSLog(@"  当前方案: %@", status.schemaName);
        NSLog(@"  是否启用: %@", status.isEnabled ? @"是" : @"否");
        NSLog(@"  是否可用: %@", status.isAvailable ? @"是" : @"否");
    }
    
    // 监控输入过程
    [self monitorInputProcess:@"hello"];
}

- (void)monitorInputProcess:(NSString *)input {
    NSLog(@"\n--- 监控输入过程: %@ ---", input);
    
    [self.inputSession clearComposition];
    
    for (int i = 0; i < input.length; i++) {
        unichar character = [input characterAtIndex:i];
        
        NSLog(@"⌨️ 输入: %c", character);
        [self.inputSession processKey:character mask:0];
        
        // 监控状态变化
        RimeContext *context = [self.inputSession context];
        if (context) {
            NSLog(@"📊 上下文状态:");
            if (context.composition) {
                NSLog(@"  组合: %@", context.composition.preedit);
            }
            if (context.menu && context.menu.candidates.count > 0) {
                NSLog(@"  候选词数: %ld", (long)context.menu.candidates.count);
            }
        }
    }
}

#pragma mark - 资源管理

- (void)demonstrateResourceManagement {
    NSLog(@"\n=== 资源管理演示 ===");
    
    // 检查维护状态
    if ([self.inputManager isMaintenancing]) {
        NSLog(@"⚠️ 系统正在维护中...");
    } else {
        NSLog(@"✅ 系统运行正常");
    }
    
    // 执行维护任务
    if ([self.inputManager performMaintenance]) {
        NSLog(@"🔧 维护任务启动成功");
    } else {
        NSLog(@"❌ 维护任务启动失败");
    }
}

- (void)cleanup {
    NSLog(@"\n=== 清理资源 ===");
    
    // 清理输入会话
    if (self.inputSession) {
        [self.inputSession clearComposition];
        self.inputSession = nil;
        NSLog(@"✅ 输入会话已清理");
    }
    
    // 销毁输入管理器
    if (self.inputManager) {
        [self.inputManager destroy];
        self.inputManager = nil;
        NSLog(@"✅ 输入管理器已销毁");
    }
}

@end

#pragma mark - 主函数演示

int main(int argc, const char * argv[]) {
    @autoreleasepool {
        NSLog(@"🚀 RimeKit 使用示例开始");
        
        // 创建示例实例
        RimeKitExample *example = [[RimeKitExample alloc] init];
        
        // 演示基础输入
        [example demonstrateBasicInput];
        
        // 演示高级功能
        [example demonstrateAdvancedFeatures];
        
        // 演示状态监控
        [example demonstrateStatusMonitoring];
        
        // 演示资源管理
        [example demonstrateResourceManagement];
        
        // 清理资源
        [example cleanup];
        
        NSLog(@"🎉 RimeKit 使用示例结束");
    }
    return 0;
}