#!/bin/bash

# RimeKit XCFramework 构建脚本 - 最终版（包含资源打包）
set -e

echo "=== RimeKit XCFramework 构建脚本 ==="

# 配置
PROJECT_NAME="RimeKit"
SCHEME_NAME="RimeKit"
FRAMEWORK_NAME="RimeKit"
RESOURCES_BUNDLE_NAME="RimeKitResources"

# 路径配置
BUILD_DIR="${PWD}/build"
OUTPUT_DIR="${PWD}/output"
XCFRAMEWORK_PATH="${OUTPUT_DIR}/${FRAMEWORK_NAME}.xcframework"

# 清理旧的构建
echo "🧹 清理旧的构建..."
rm -rf "${BUILD_DIR}"
rm -rf "${OUTPUT_DIR}"
mkdir -p "${BUILD_DIR}"
mkdir -p "${OUTPUT_DIR}"

# 源文件根目录
SOURCE_ROOT="${PWD}"
ASSETS_DIR="${SOURCE_ROOT}/assets"
PREBUILT_DIR="${ASSETS_DIR}/prebuilt"
RIME_DATA_DIR="${ASSETS_DIR}/rime-data"

echo "📦 开始构建 XCFramework..."

# 1. 构建 iOS 设备版本 (仅 arm64)
echo "📱 构建 iOS 设备版本 (arm64)..."
xcodebuild archive \
    -project RimeKit.xcodeproj \
    -scheme RimeKit \
    -archivePath build/ios-device.xcarchive \
    -sdk iphoneos \
    -destination "generic/platform=iOS" \
    SKIP_INSTALL=NO \
    BUILD_LIBRARY_FOR_DISTRIBUTION=YES \
    ARCHS="arm64" \
    VALID_ARCHS="arm64" \
    ONLY_ACTIVE_ARCH=NO \
    -quiet

# 2. 准备创建 XCFramework
echo "🔨 准备创建 XCFramework..."

# 获取静态库路径
DEVICE_LIB="build/ios-device.xcarchive/Products/usr/local/lib/libRimeKit.a"

# 检查文件是否存在
if [ ! -f "$DEVICE_LIB" ]; then
    echo "❌ 错误：找不到设备库文件"
    exit 1
fi

# 显示架构信息
echo "设备库架构："
lipo -info "$DEVICE_LIB"

# 创建头文件目录
echo "📋 准备头文件..."
DEVICE_HEADERS="build/device-headers"

mkdir -p "$DEVICE_HEADERS"

# 复制所有头文件
cp -R RimeKit/*.h "$DEVICE_HEADERS/" 2>/dev/null || true

# 复制 include 目录
if [ -d "RimeKit/include" ]; then
    mkdir -p "$DEVICE_HEADERS/include"
    cp -R RimeKit/include/*.h "$DEVICE_HEADERS/include/" 2>/dev/null || true
fi

# 3. 创建 XCFramework
echo "🔨 创建 XCFramework..."
xcodebuild -create-xcframework \
    -library "$DEVICE_LIB" \
    -headers "$DEVICE_HEADERS" \
    -output "${XCFRAMEWORK_PATH}"

# 4. 创建资源 Bundle 在 output 根目录
if [ -d "${XCFRAMEWORK_PATH}" ]; then
    echo "✅ XCFramework 创建成功！"
    echo "📚 创建资源 Bundle..."
    
    # 创建资源 Bundle
    BUNDLE_PATH="${OUTPUT_DIR}/${RESOURCES_BUNDLE_NAME}.bundle"
    mkdir -p "${BUNDLE_PATH}"
    
    echo "  📋 复制资源文件..."
    
    # 复制 prebuilt 文件
    if [ -d "${PREBUILT_DIR}" ]; then
        cp -R "${PREBUILT_DIR}" "${BUNDLE_PATH}/"
    fi
    
    # 复制 rime-data 文件
    if [ -d "${RIME_DATA_DIR}" ]; then
        cp -R "${RIME_DATA_DIR}" "${BUNDLE_PATH}/"
    fi
    
    # 创建 Info.plist for bundle
    cat > "${BUNDLE_PATH}/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleIdentifier</key>
    <string>com.rimekit.resources</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>${RESOURCES_BUNDLE_NAME}</string>
    <key>CFBundlePackageType</key>
    <string>BNDL</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1</string>
</dict>
</plist>
EOF
    
    # 5. 验证创建结果
    echo "✅ 验证输出结构..."
    echo "输出目录内容："
    ls -la "${OUTPUT_DIR}/"
    
    if [ -d "${BUNDLE_PATH}" ]; then
        echo ""
        echo "资源 Bundle 内容："
        ls -la "${BUNDLE_PATH}/" | head -10
    fi
    
    # 创建集成文档
    cat > output/Integration.md << 'EOF'
# RimeKit XCFramework 集成指南

## 概述
RimeKit.xcframework 是一个静态库 XCFramework，支持 iOS 设备。

## 支持的架构
- iOS 设备: arm64

## 集成步骤

### 1. 添加 XCFramework
1. 将 \`RimeKit.xcframework\` 拖入你的 Xcode 项目
2. 在弹出的对话框中选择 "Copy items if needed"
3. 确保选中你的 target

### 2. 配置链接
1. 选择你的项目 target
2. 在 "General" 标签页找到 "Frameworks, Libraries, and Embedded Content"
3. 确保 RimeKit.xcframework 的 "Embed" 设置为 "Do Not Embed"

### 3. 添加依赖库
确保项目中包含以下依赖的 XCFramework：
- librime.xcframework
- boost_*.xcframework (atomic, filesystem, locale, regex, system)
- icu*.xcframework (data, i18n, io, uc)
- libglog.xcframework
- libleveldb.xcframework
- libmarisa.xcframework
- libopencc.xcframework
- libyaml-cpp.xcframework

### 4. 词库文件配置
RimeKit 需要词库文件才能正常工作。请按以下步骤配置：

1. 将 RimeKitResources.bundle 添加到项目中
2. 确保在 "Build Phases" -> "Copy Bundle Resources" 中包含此 bundle
3. 资源 bundle 包含：
   - prebuilt/: 预编译的词库文件
   - rime-data/: Rime 配置文件

### 5. 使用示例

```objc
#import <RimeKit/RimeKit.h>

@implementation YourViewController

- (void)setupRime {
    // 1. 获取文档目录
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDir = [paths firstObject];
    
    // 2. 创建配置
    RimeTraits *traits = [RimeTraits defaultTraitsWithBundleIdentifier:@"com.example.app"
                                                         documentsDir:documentsDir
                                                              version:@"1.0.0"];
    
    // 3. 初始化引擎
    RimeEngine *engine = [RimeEngine sharedEngine];
    if ([engine initializeWithTraits:traits]) {
        NSLog(@"RimeKit 初始化成功");
        
        // 4. 创建输入会话
        RimeInputManager *manager = [RimeInputManager sharedManager];
        RimeInputSession *session = [manager createSession];
        session.delegate = self;
        
        // 5. 选择输入方案（重要！）
        [session selectSchema:@"rime_ice"];  // 或 @"t9"
        
        // 6. 开始输入
        [session processCharacter:'n'];
        [session processCharacter:'i'];
        [session processCharacter:'h'];
        [session processCharacter:'a'];
        [session processCharacter:'o'];
    }
}

// RimeInputSessionDelegate 方法
- (void)onCandidatesUpdate:(RimeCandidateList *)candidates {
    // 处理候选词更新
    for (int i = 0; i < candidates.count; i++) {
        RimeCandidate *candidate = [candidates candidateAtIndex:i];
        NSLog(@"候选词 %d: %@", i, candidate.text);
    }
}

- (void)onCompositionUpdate:(RimeComposition *)composition {
    // 处理组合更新
    NSLog(@"当前输入: %@", composition.preedit);
}

- (void)onTextCommit:(NSString *)text {
    // 处理文本提交
    NSLog(@"提交文本: %@", text);
}

@end
```

## 注意事项

1. **必须选择输入方案**：在创建会话后，必须调用 `selectSchema` 选择输入方案
2. **必须添加资源 Bundle**：RimeKitResources.bundle 包含必要的词库和配置文件
3. 这是一个静态库，不要设置为 "Embed & Sign"
4. 确保所有依赖库都已添加到项目中
5. 初始化时会自动创建必要的数据目录
6. 资源文件会自动从 Bundle 复制到应用沙盒目录

## 故障排除

### 无法获取候选词
- 确保已调用 `selectSchema` 选择输入方案
- 检查资源文件是否正确加载：使用 `[RimeKitResourceLoader checkResourcesAvailable]`

### 链接错误
如果遇到 "undefined symbols" 错误，请检查：
1. 所有依赖的 XCFramework 是否已添加
2. Build Settings 中的 "Other Linker Flags" 是否包含 "-ObjC"
3. 架构设置是否正确

### 运行时错误
如果遇到运行时错误：
1. 检查控制台日志获取详细错误信息
2. 确保有写入权限到用户数据目录
3. 验证资源 Bundle 是否存在并可访问

## 版本信息
- RimeKit 版本: 1.0.0
- 最低 iOS 版本: 12.0
- 构建时间: $(date)
EOF
    
    # 清理临时文件
    echo ""
    echo "🧹 清理临时文件..."
    rm -rf build
    
    echo ""
    echo "✅ 构建完成！"
    echo "📍 输出位置:"
    echo "   - XCFramework: ${XCFRAMEWORK_PATH}"
    echo "   - 资源 Bundle: ${OUTPUT_DIR}/${RESOURCES_BUNDLE_NAME}.bundle"
    echo ""
    echo "使用说明:"
    echo "1. 将 ${XCFRAMEWORK_PATH} 拖入你的 Xcode 项目"
    echo "2. 将 ${OUTPUT_DIR}/${RESOURCES_BUNDLE_NAME}.bundle 添加到项目资源中"
    echo "3. 确保在 'Do Not Embed' 中设置（静态库）"
    echo "4. 在创建会话后必须调用 selectSchema 选择输入方案"
    
else
    echo "❌ XCFramework 创建失败"
    exit 1
fi

echo ""
echo "=== 完成 ==="