#import "RimeTraits.h"

@implementation RimeTraits

- (instancetype)initWithSharedDataDir:(NSString *)sharedDataDir
                         userDataDir:(NSString *)userDataDir
                    distributionName:(NSString *)distributionName
                distributionCodeName:(NSString *)distributionCodeName
                 distributionVersion:(NSString *)distributionVersion
                            appName:(NSString *)appName {
    self = [super init];
    if (self) {
        _sharedDataDir = [sharedDataDir copy];
        _userDataDir = [userDataDir copy];
        _distributionName = [distributionName copy];
        _distributionCodeName = [distributionCodeName copy];
        _distributionVersion = [distributionVersion copy];
        _appName = [appName copy];
        _minLogLevel = 0; // 默认INFO级别
    }
    return self;
}

+ (instancetype)defaultTraitsWithBundleIdentifier:(NSString *)bundleIdentifier
                                     documentsDir:(NSString *)documentsDir
                                          version:(NSString *)version {
    NSString *userDataDir = [documentsDir stringByAppendingPathComponent:@"rime"];
    NSString *sharedDataDir = [documentsDir stringByAppendingPathComponent:@"rime-data"];
    
    RimeTraits *traits = [[RimeTraits alloc] initWithSharedDataDir:sharedDataDir
                                                       userDataDir:userDataDir
                                                  distributionName:@"Rime"
                                              distributionCodeName:@"librime-ios"
                                               distributionVersion:version
                                                          appName:[NSString stringWithFormat:@"rime.%@", 
                                                                  [bundleIdentifier stringByReplacingOccurrencesOfString:@"." withString:@"_"]]];
    
    traits.logDir = [userDataDir stringByAppendingPathComponent:@"log"];
    traits.prebuiltDataDir = [sharedDataDir stringByAppendingPathComponent:@"build"];
    traits.stagingDir = [userDataDir stringByAppendingPathComponent:@"build"];
    
    return traits;
}

@end