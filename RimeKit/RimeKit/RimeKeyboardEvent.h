#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 键盘事件类
 * 封装键盘输入事件，包括按键码和修饰符
 */
@interface RimeKeyboardEvent : NSObject

/** 按键码 */
@property (nonatomic, readonly) int keyCode;

/** 修饰符（如Ctrl、Alt、Shift等） */
@property (nonatomic, readonly) int modifiers;

// 常用按键码定义 - 使用XKB标准键码
extern const int RimeKeySpace;          // 空格
extern const int RimeKeyReturn;         // 回车
extern const int RimeKeyBackspace;      // 退格
extern const int RimeKeyDelete;         // 删除
extern const int RimeKeyEscape;         // ESC
extern const int RimeKeyTab;            // Tab
extern const int RimeKeyLeft;           // 左箭头
extern const int RimeKeyUp;             // 上箭头
extern const int RimeKeyRight;          // 右箭头
extern const int RimeKeyDown;           // 下箭头
extern const int RimeKeyPageUp;         // Page Up
extern const int RimeKeyPageDown;       // Page Down
extern const int RimeKeyHome;           // Home
extern const int RimeKeyEnd;            // End

// 修饰符定义
extern const int RimeModifierNone;      // 无修饰符
extern const int RimeModifierShift;     // Shift
extern const int RimeModifierCtrl;      // Ctrl
extern const int RimeModifierAlt;       // Alt
extern const int RimeModifierMeta;      // Meta (Win/Cmd)

/**
 * 创建键盘事件
 * @param keyCode 按键码
 * @param modifiers 修饰符
 */
- (instancetype)initWithKeyCode:(int)keyCode modifiers:(int)modifiers;

/**
 * 创建键盘事件（无修饰符）
 * @param keyCode 按键码
 */
- (instancetype)initWithKeyCode:(int)keyCode;

// ================ 静态工厂方法 ================

/**
 * 创建空格键事件
 */
+ (instancetype)space;

/**
 * 创建回车键事件
 */
+ (instancetype)enter;

/**
 * 创建退格键事件
 */
+ (instancetype)backspace;

/**
 * 创建删除键事件
 */
+ (instancetype)delete;

/**
 * 创建ESC键事件
 */
+ (instancetype)escape;

/**
 * 创建Tab键事件
 */
+ (instancetype)tab;

/**
 * 创建左箭头键事件
 */
+ (instancetype)left;

/**
 * 创建右箭头键事件
 */
+ (instancetype)right;

/**
 * 创建上箭头键事件
 */
+ (instancetype)up;

/**
 * 创建下箭头键事件
 */
+ (instancetype)down;

/**
 * 创建Page Up键事件
 */
+ (instancetype)pageUp;

/**
 * 创建Page Down键事件
 */
+ (instancetype)pageDown;

/**
 * 从字符创建键盘事件
 * @param character 字符
 */
+ (instancetype)fromCharacter:(char)character;

// ================ 辅助方法 ================

/**
 * 检查是否包含指定修饰符
 * @param modifier 要检查的修饰符
 */
- (BOOL)hasModifier:(int)modifier;

/**
 * 检查是否按下了Shift键
 */
- (BOOL)hasShift;

/**
 * 检查是否按下了Ctrl键
 */
- (BOOL)hasCtrl;

/**
 * 检查是否按下了Alt键
 */
- (BOOL)hasAlt;

/**
 * 检查是否按下了Meta键
 */
- (BOOL)hasMeta;

/**
 * 检查是否为字符键
 */
- (BOOL)isCharacter;

/**
 * 检查是否为控制键
 */
- (BOOL)isControlKey;

/**
 * 检查是否为导航键
 */
- (BOOL)isNavigationKey;

/**
 * 创建带有附加修饰符的新事件
 * @param additionalModifiers 要添加的修饰符
 */
- (instancetype)withModifiers:(int)additionalModifiers;

/**
 * 获取键盘事件的描述字符串
 */
- (NSString *)eventDescription;

@end

NS_ASSUME_NONNULL_END