#import "RimeKeyboardEvent.h"

// 常用按键码定义
const int RimeKeySpace = 0x20;
const int RimeKeyReturn = 0xFF0D;
const int RimeKeyBackspace = 0xFF08;
const int RimeKeyDelete = 0xFFFF;
const int RimeKeyEscape = 0xFF1B;
const int RimeKeyTab = 0xFF09;
const int RimeKeyLeft = 0xFF51;
const int RimeKeyUp = 0xFF52;
const int RimeKeyRight = 0xFF53;
const int RimeKeyDown = 0xFF54;
const int RimeKeyPageUp = 0xFF55;
const int RimeKeyPageDown = 0xFF56;
const int RimeKeyHome = 0xFF50;
const int RimeKeyEnd = 0xFF57;

// 修饰符定义
const int RimeModifierNone = 0x00;
const int RimeModifierShift = 0x01;
const int RimeModifierCtrl = 0x02;
const int RimeModifierAlt = 0x04;
const int RimeModifierMeta = 0x08;

@implementation RimeKeyboardEvent

- (instancetype)initWithKeyCode:(int)keyCode modifiers:(int)modifiers {
    self = [super init];
    if (self) {
        _keyCode = keyCode;
        _modifiers = modifiers;
    }
    return self;
}

- (instancetype)initWithKeyCode:(int)keyCode {
    return [self initWithKeyCode:keyCode modifiers:RimeModifierNone];
}

#pragma mark - 静态工厂方法

+ (instancetype)space {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeySpace];
}

+ (instancetype)enter {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyReturn];
}

+ (instancetype)backspace {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyBackspace];
}

+ (instancetype)delete {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyDelete];
}

+ (instancetype)escape {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyEscape];
}

+ (instancetype)tab {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyTab];
}

+ (instancetype)left {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyLeft];
}

+ (instancetype)right {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyRight];
}

+ (instancetype)up {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyUp];
}

+ (instancetype)down {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyDown];
}

+ (instancetype)pageUp {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyPageUp];
}

+ (instancetype)pageDown {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:RimeKeyPageDown];
}

+ (instancetype)fromCharacter:(char)character {
    // 直接使用字符的ASCII值，不进行大小写转换
    // 这样保持拼音输入的小写字符
    return [[RimeKeyboardEvent alloc] initWithKeyCode:(int)character];
}

#pragma mark - 辅助方法

- (BOOL)hasModifier:(int)modifier {
    return (self.modifiers & modifier) != 0;
}

- (BOOL)hasShift {
    return [self hasModifier:RimeModifierShift];
}

- (BOOL)hasCtrl {
    return [self hasModifier:RimeModifierCtrl];
}

- (BOOL)hasAlt {
    return [self hasModifier:RimeModifierAlt];
}

- (BOOL)hasMeta {
    return [self hasModifier:RimeModifierMeta];
}

- (BOOL)isCharacter {
    return self.keyCode >= 32 && self.keyCode <= 126;
}

- (BOOL)isControlKey {
    return self.keyCode == RimeKeyBackspace || self.keyCode == RimeKeyDelete ||
           self.keyCode == RimeKeyEscape || self.keyCode == RimeKeyTab ||
           self.keyCode == RimeKeyReturn || self.keyCode == RimeKeySpace ||
           [self isNavigationKey];
}

- (BOOL)isNavigationKey {
    return self.keyCode == RimeKeyLeft || self.keyCode == RimeKeyRight ||
           self.keyCode == RimeKeyUp || self.keyCode == RimeKeyDown ||
           self.keyCode == RimeKeyHome || self.keyCode == RimeKeyEnd ||
           self.keyCode == RimeKeyPageUp || self.keyCode == RimeKeyPageDown;
}

- (instancetype)withModifiers:(int)additionalModifiers {
    return [[RimeKeyboardEvent alloc] initWithKeyCode:self.keyCode 
                                             modifiers:self.modifiers | additionalModifiers];
}

- (NSString *)eventDescription {
    NSMutableString *description = [NSMutableString string];
    
    // 添加修饰符
    if ([self hasCtrl]) [description appendString:@"Ctrl+"];
    if ([self hasAlt]) [description appendString:@"Alt+"];
    if ([self hasShift]) [description appendString:@"Shift+"];
    if ([self hasMeta]) [description appendString:@"Meta+"];
    
    // 添加按键名称
    switch (self.keyCode) {
        case RimeKeySpace: [description appendString:@"Space"]; break;
        case RimeKeyReturn: [description appendString:@"Enter"]; break;
        case RimeKeyBackspace: [description appendString:@"Backspace"]; break;
        case RimeKeyDelete: [description appendString:@"Delete"]; break;
        case RimeKeyEscape: [description appendString:@"Escape"]; break;
        case RimeKeyTab: [description appendString:@"Tab"]; break;
        case RimeKeyLeft: [description appendString:@"Left"]; break;
        case RimeKeyRight: [description appendString:@"Right"]; break;
        case RimeKeyUp: [description appendString:@"Up"]; break;
        case RimeKeyDown: [description appendString:@"Down"]; break;
        case RimeKeyPageUp: [description appendString:@"PageUp"]; break;
        case RimeKeyPageDown: [description appendString:@"PageDown"]; break;
        case RimeKeyHome: [description appendString:@"Home"]; break;
        case RimeKeyEnd: [description appendString:@"End"]; break;
        default:
            if ([self isCharacter]) {
                [description appendFormat:@"%c", (char)self.keyCode];
            } else {
                [description appendFormat:@"Key(%d)", self.keyCode];
            }
            break;
    }
    
    return description;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"RimeKeyboardEvent{keyCode=%d, modifiers=%d, description='%@'}",
            self.keyCode, self.modifiers, [self eventDescription]];
}

- (BOOL)isEqual:(id)object {
    if (self == object) return YES;
    if (![object isKindOfClass:[RimeKeyboardEvent class]]) return NO;
    RimeKeyboardEvent *other = (RimeKeyboardEvent *)object;
    return self.keyCode == other.keyCode && self.modifiers == other.modifiers;
}

- (NSUInteger)hash {
    return self.keyCode * 31 + self.modifiers;
}

@end