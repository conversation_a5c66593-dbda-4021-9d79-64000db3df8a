#import "RimeCandidate.h"

@implementation RimeCandidate

- (instancetype)initWithText:(NSString *)text comment:(nullable NSString *)comment {
    self = [super init];
    if (self) {
        _text = [text copy];
        _comment = [comment copy];
    }
    return self;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"RimeCandidate{text='%@', comment='%@'}", 
            self.text, self.comment ?: @""];
}

@end