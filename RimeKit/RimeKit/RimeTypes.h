//
//  RimeTypes.h
//  RimeKit
//
//  C结构体和Objective-C对象之间的桥接
//

#ifndef RimeTypes_h
#define RimeTypes_h

#import <Foundation/Foundation.h>
#import "rime_api.h"

// 前向声明Objective-C类
@class RKTraits;
@class RKCommit;
@class RKStatus;
@class RKComposition;
@class RKCandidate;
@class RKMenu;
@class RKContext;
@class RKSchemaListItem;
@class RKSchemaList;

// C结构体名称（来自rime_api.h）
// typedef struct rime_traits_t RimeTraits;
// typedef struct rime_commit_t RimeCommit;
// typedef struct rime_status_t RimeStatus;
// typedef struct rime_composition_t RimeComposition;
// typedef struct rime_candidate_t RimeCandidate;
// typedef struct rime_menu_t RimeMenu;
// typedef struct rime_context_t RimeContext;

#endif /* RimeTypes_h */