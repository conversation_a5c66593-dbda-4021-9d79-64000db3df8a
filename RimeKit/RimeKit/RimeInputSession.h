#import <Foundation/Foundation.h>
#import "RimeEngine.h"

@class RimeKeyboardEvent;
@class RimeCandidateList;
@class RimeComposition;

NS_ASSUME_NONNULL_BEGIN

/**
 * 输入会话监听器协议
 */
@protocol RimeInputSessionDelegate <NSObject>

@optional
/**
 * 当有文本需要提交时调用
 * @param text 要提交的文本
 */
- (void)onTextCommit:(NSString *)text;

/**
 * 当候选词列表更新时调用
 * @param candidates 候选词列表
 */
- (void)onCandidatesUpdate:(RimeCandidateList *)candidates;

/**
 * 当输入组合更新时调用
 * @param composition 当前组合状态
 */
- (void)onCompositionUpdate:(nullable RimeComposition *)composition;

@end

/**
 * 输入会话类
 * 管理单个输入上下文，处理用户输入和候选词选择
 */
@interface RimeInputSession : NSObject

@property (nonatomic, readonly) RimeSessionId sessionId;
@property (nonatomic, readonly) BOOL isActive;
@property (nonatomic, weak) id<RimeInputSessionDelegate> delegate;

/**
 * 初始化方法（内部使用）
 * @param sessionId 会话ID
 * @param engine Rime引擎
 */
- (instancetype)initWithSessionId:(RimeSessionId)sessionId engine:(RimeEngine *)engine NS_DESIGNATED_INITIALIZER;
- (instancetype)init NS_UNAVAILABLE;

/**
 * 处理字符输入
 * @param character 输入的字符
 * @return 是否处理成功
 */
- (BOOL)processCharacter:(char)character;

/**
 * 处理特殊按键（如空格、回车、删除等）
 * @param keyEvent 按键事件
 * @return 是否处理成功
 */
- (BOOL)processKey:(RimeKeyboardEvent *)keyEvent;

/**
 * 处理按键输入并同步返回候选词列表
 * @param keyEvent 按键事件
 * @return 处理后的候选词列表，如果处理失败返回nil
 */
- (nullable RimeCandidateList *)inputKey:(RimeKeyboardEvent *)keyEvent;

/**
 * 选择候选词
 * @param index 候选词索引
 * @return 是否选择成功
 */
- (BOOL)selectCandidate:(NSInteger)index;

/**
 * 删除候选词
 * @param index 候选词索引
 * @return 是否删除成功
 */
- (BOOL)deleteCandidate:(NSInteger)index;

/**
 * 翻页操作
 * @param backward 是否向后翻页
 * @return 是否翻页成功
 */
- (BOOL)changePage:(BOOL)backward;

/**
 * 翻页操作并同步返回候选词列表
 * @param backward 是否向后翻页
 * @return 翻页后的候选词列表，如果翻页失败返回nil
 */
- (nullable RimeCandidateList *)changePageAndGetCandidates:(BOOL)backward;

/**
 * 上一页候选词并同步返回候选词列表
 * @return 上一页的候选词列表，如果翻页失败返回nil
 */
- (nullable RimeCandidateList *)pageUp;

/**
 * 下一页候选词并同步返回候选词列表
 * @return 下一页的候选词列表，如果翻页失败返回nil
 */
- (nullable RimeCandidateList *)pageDown;

/**
 * 提交当前组合
 * @return 是否提交成功
 */
- (BOOL)commitComposition;

/**
 * 清空当前组合
 */
- (void)clearComposition;

/**
 * 获取当前输入的原始字符串
 * @return 原始输入字符串
 */
- (nullable NSString *)currentInput;

/**
 * 设置输入内容
 * @param input 要设置的输入内容
 * @return 是否设置成功
 */
- (BOOL)setInput:(NSString *)input;

/**
 * 获取当前候选词列表
 * @return 候选词列表
 */
- (nullable RimeCandidateList *)candidates;

/**
 * 选择输入方案
 * @param schemaId 方案ID
 * @return 是否选择成功
 */
- (BOOL)selectSchema:(NSString *)schemaId;

/**
 * 获取当前会话选择的输入方案
 * @return 当前方案ID，失败返回nil
 */
- (nullable NSString *)currentSchema;

/**
 * 更新上下文（手动触发更新）
 */
- (void)updateContext;

/**
 * 关闭会话，释放资源
 */
- (void)close;

@end

NS_ASSUME_NONNULL_END