#import <Foundation/Foundation.h>
#import "RimeInputMode.h"

@class RimeKeyboardEvent;
@class RimeCandidateList;

NS_ASSUME_NONNULL_BEGIN

/**
 * 输入法回调协议
 * 仅包含候选词更新通知
 */
@protocol RimeManagerDelegate <NSObject>
@optional
/**
 * 当候选词更新时调用
 * @param candidateList 候选词列表
 */
- (void)onUpdateCandidateWords:(RimeCandidateList *)candidateList;
@end

/**
 * RimeKit 极简管理器
 * 参考 Android 实现，提供核心方法
 * 隐藏底层复杂性，提供最简洁的API
 */
@interface RimeManager : NSObject

/**
 * 获取单例实例
 * @return RimeManager 单例
 */
+ (instancetype)sharedManager;

/**
 * 初始化输入法引擎
 * 完成所有初始化操作
 * @return 初始化是否成功
 */
- (BOOL)initializeEngine;

/**
 * 输入按键
 * @param keyEvent 按键事件
 * @return 处理后的候选词列表，如果处理失败返回nil
 */
- (nullable RimeCandidateList *)inputKey:(RimeKeyboardEvent *)keyEvent;

/**
 * 设置输入法回调
 * @param delegate 回调委托
 */
- (void)setImeCallback:(nullable id<RimeManagerDelegate>)delegate;

/**
 * 上一页候选词
 * @return 上一页的候选词列表，如果翻页失败返回nil
 */
- (nullable RimeCandidateList *)pageUp;

/**
 * 下一页候选词
 * @return 下一页的候选词列表，如果翻页失败返回nil
 */
- (nullable RimeCandidateList *)pageDown;

/**
 * 重置session
 * 清空当前输入状态，重新创建会话
 */
- (void)reset;

/**
 * 切换输入模式
 * @param inputMode 输入模式枚举
 * @return 切换是否成功
 */
- (BOOL)switchInputMode:(RimeInputMode)inputMode;

/**
 * 切换输入模式（通过整型值）
 * @param modeValue 输入模式值：0-拼音，1-九键
 * @return 切换是否成功
 */
- (BOOL)switchInputModeWithValue:(NSInteger)modeValue;

/**
 * 切换输入方案（最底层方法）
 * @param schemaId 输入方案ID，如 "rime_ice"、"t9" 等
 * @return 切换是否成功
 */
- (BOOL)switchSchema:(NSString *)schemaId;

@end

NS_ASSUME_NONNULL_END