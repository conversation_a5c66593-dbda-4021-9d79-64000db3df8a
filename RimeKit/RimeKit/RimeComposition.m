#import "RimeComposition.h"

@implementation RimeComposition

- (instancetype)initWithLength:(NSInteger)length
                     cursorPos:(NSInteger)cursorPos
                      selStart:(NSInteger)selStart
                        selEnd:(NSInteger)selEnd
                       preedit:(NSString *)preedit {
    self = [super init];
    if (self) {
        _length = length;
        _cursorPos = cursorPos;
        _selStart = selStart;
        _selEnd = selEnd;
        _preedit = [preedit copy];
    }
    return self;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"RimeComposition{length=%ld, cursorPos=%ld, selStart=%ld, selEnd=%ld, preedit='%@'}",
            (long)self.length, (long)self.cursorPos, (long)self.selStart, (long)self.selEnd, self.preedit];
}

@end