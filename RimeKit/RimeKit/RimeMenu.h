#import <Foundation/Foundation.h>

@class RimeCandidate;

NS_ASSUME_NONNULL_BEGIN

/**
 * Rime候选词菜单
 * 对应librime的RimeMenu结构体
 */
@interface RimeMenu : NSObject

@property (nonatomic, readonly) NSInteger pageSize;
@property (nonatomic, readonly) NSInteger pageNo;
@property (nonatomic, readonly) BOOL isLastPage;
@property (nonatomic, readonly) NSInteger highlightedCandidateIndex;
@property (nonatomic, copy, readonly) NSArray<RimeCandidate *> *candidates;
@property (nonatomic, copy, readonly, nullable) NSString *selectKeys;

- (instancetype)initWithPageSize:(NSInteger)pageSize
                          pageNo:(NSInteger)pageNo
                      isLastPage:(BOOL)isLastPage
          highlightedCandidateIndex:(NSInteger)highlightedCandidateIndex
                      candidates:(NSArray<RimeCandidate *> *)candidates
                      selectKeys:(nullable NSString *)selectKeys;

@end

NS_ASSUME_NONNULL_END