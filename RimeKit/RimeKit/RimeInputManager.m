#import "RimeInputManager.h"
#import "RimeInputSession.h"
#import "RimeEngine.h"
#import "RimeTraits.h"
#import "RimeSchemaList.h"

@interface RimeInputManager ()
@property (nonatomic, strong) RimeEngine *engine;
@property (nonatomic, assign) BOOL isInitialized;
@end

@implementation RimeInputManager

+ (instancetype)sharedManager {
    static RimeInputManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[RimeInputManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _isInitialized = NO;
        _engine = [RimeEngine sharedEngine];
    }
    return self;
}

- (BOOL)initialize {
    if (self.isInitialized) {
        NSLog(@"[RimeInputManager] SDK already initialized");
        return YES;
    }
    
    @try {
        // 获取默认路径
        NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
        NSString *documentsPath = paths.firstObject;
        NSString *bundleIdentifier = [[NSBundle mainBundle] bundleIdentifier];
        NSString *version = @"1.0.0";
        
        return [self initializeWithDocumentsPath:documentsPath
                                bundleIdentifier:bundleIdentifier
                                         version:version];
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputManager] Exception during initialization: %@", exception);
        return NO;
    }
}

- (BOOL)initializeWithDocumentsPath:(NSString *)documentsPath
                    bundleIdentifier:(NSString *)bundleIdentifier
                             version:(NSString *)version {
    if (self.isInitialized) {
        NSLog(@"[RimeInputManager] SDK already initialized");
        return YES;
    }
    
    @try {
        NSLog(@"[RimeInputManager] Initializing with documentsPath: %@", documentsPath);
        
        // 创建默认配置
        RimeTraits *traits = [RimeTraits defaultTraitsWithBundleIdentifier:bundleIdentifier
                                                              documentsDir:documentsPath
                                                                   version:version];
        
        // 设置最低日志级别用于调试
        traits.minLogLevel = 0; // 显示所有日志用于调试
        
        NSLog(@"[RimeInputManager] Rime traits configured:");
        NSLog(@"  User data dir: %@", traits.userDataDir);
        NSLog(@"  Shared data dir: %@", traits.sharedDataDir);
        NSLog(@"  App name: %@", traits.appName);
        NSLog(@"  Distribution name: %@", traits.distributionName);
        NSLog(@"  Log dir: %@", traits.logDir);
        
        // 初始化引擎
        BOOL result = [self.engine initializeWithTraits:traits];
        if (result) {
            self.isInitialized = YES;
            NSLog(@"[RimeInputManager] RimeInputManager initialized successfully");
        } else {
            NSLog(@"[RimeInputManager] Failed to initialize RimeInputManager");
        }
        
        return result;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputManager] Exception during initialization: %@", exception);
        return NO;
    }
}

- (nullable RimeInputSession *)createSession {
    [self checkInitialized];
    
    RimeSessionId sessionId = [self.engine createSession];
    if (sessionId != 0) {
        NSLog(@"[RimeInputManager] Created input session: %lu", (unsigned long)sessionId);
        return [[RimeInputSession alloc] initWithSessionId:sessionId engine:self.engine];
    } else {
        NSLog(@"[RimeInputManager] Failed to create input session");
        return nil;
    }
}

- (NSString *)version {
    if (self.engine) {
        return [self.engine getVersion];
    }
    return @"unknown";
}

- (void)destroy {
    if (self.isInitialized) {
        if (self.engine) {
            [self.engine finalize];
        }
        self.isInitialized = NO;
        NSLog(@"[RimeInputManager] RimeInputManager destroyed");
    }
}

- (BOOL)performMaintenance {
    [self checkInitialized];
    return [self.engine startMaintenance:NO];
}

- (BOOL)isMaintenancing {
    [self checkInitialized];
    return [self.engine isMaintenancing];
}

- (nullable NSArray *)schemaList {
    [self checkInitialized];
    if (self.engine) {
        return [self.engine getSchemaList];
    }
    return nil;
}

#pragma mark - Private Methods

- (void)checkInitialized {
    if (!self.isInitialized) {
        [NSException raise:NSInternalInconsistencyException 
                    format:@"RimeInputManager not initialized. Call initialize first."];
    }
}

@end