#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 输入模式枚举
 */
typedef NS_ENUM(NSInteger, RimeInputMode) {
    /**
     * 拼音输入模式
     */
    RimeInputModePinyin = 0,
    
    /**
     * 9键输入模式
     */
    RimeInputModeT9 = 1
};

/**
 * 输入模式工具类
 */
@interface RimeInputModeHelper : NSObject

/**
 * 根据输入模式获取对应的 schema ID
 * @param inputMode 输入模式
 * @return schema ID
 */
+ (NSString *)schemaIdForInputMode:(RimeInputMode)inputMode;

/**
 * 根据整型值获取输入模式
 * @param value 模式值
 * @param inputMode 输出参数，对应的输入模式
 * @return 是否找到对应的输入模式
 */
+ (BOOL)inputModeFromValue:(NSInteger)value inputMode:(RimeInputMode *)inputMode;

@end

NS_ASSUME_NONNULL_END