#import <Foundation/Foundation.h>

@class RimeMenu;
@class RimeCandidate;

NS_ASSUME_NONNULL_BEGIN

/**
 * 候选词列表管理类
 * 提供对候选词的便捷访问和操作
 */
@interface RimeCandidateList : NSObject

@property (nonatomic, readonly) NSInteger pageSize;
@property (nonatomic, readonly) NSInteger pageNumber;
@property (nonatomic, readonly) BOOL hasNextPage;
@property (nonatomic, readonly) BOOL hasPreviousPage;

/**
 * 创建空的候选词列表
 */
- (instancetype)init;

/**
 * 基于RimeMenu创建候选词列表
 * @param menu Rime菜单对象
 */
- (instancetype)initWithMenu:(RimeMenu *)menu;

/**
 * 获取候选词数量
 */
- (NSInteger)count;

/**
 * 检查是否为空
 */
- (BOOL)isEmpty;

/**
 * 获取指定位置的候选词
 * @param index 索引位置
 * @return 候选词对象，若索引无效则返回nil
 */
- (nullable RimeCandidate *)candidateAtIndex:(NSInteger)index;

/**
 * 获取指定位置候选词的文本
 * @param index 索引位置
 * @return 候选词文本，若索引无效则返回nil
 */
- (nullable NSString *)textAtIndex:(NSInteger)index;

/**
 * 获取指定位置候选词的注释
 * @param index 索引位置
 * @return 候选词注释，若索引无效或无注释则返回nil
 */
- (nullable NSString *)commentAtIndex:(NSInteger)index;

/**
 * 获取所有候选词文本列表
 * @return 候选词文本列表
 */
- (NSArray<NSString *> *)allTexts;

/**
 * 获取所有候选词对象的副本
 * @return 候选词对象列表
 */
- (NSArray<RimeCandidate *> *)allCandidates;

/**
 * 查找候选词文本的索引
 * @param text 要查找的候选词文本
 * @return 索引位置，未找到返回-1
 */
- (NSInteger)indexOfText:(NSString *)text;

/**
 * 检查指定索引是否有效
 * @param index 索引位置
 */
- (BOOL)isValidIndex:(NSInteger)index;

/**
 * 获取候选词的格式化显示文本
 * @param index 索引位置
 * @param showComment 是否显示注释
 * @return 格式化的显示文本
 */
- (nullable NSString *)displayTextAtIndex:(NSInteger)index showComment:(BOOL)showComment;

/**
 * 获取候选词的标签（通常是序号）
 * @param index 索引位置
 * @return 候选词标签
 */
- (NSString *)labelAtIndex:(NSInteger)index;

@end

NS_ASSUME_NONNULL_END