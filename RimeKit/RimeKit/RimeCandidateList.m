#import "RimeCandidateList.h"
#import "RimeMenu.h"
#import "RimeCandidate.h"

@interface RimeCandidateList ()
@property (nonatomic, strong) NSMutableArray<RimeCandidate *> *candidates;
@property (nonatomic, assign) NSInteger pageSize;
@property (nonatomic, assign) NSInteger pageNumber;
@property (nonatomic, assign) BOOL hasNextPage;
@property (nonatomic, assign) BOOL hasPreviousPage;
@end

@implementation RimeCandidateList

- (instancetype)init {
    self = [super init];
    if (self) {
        _candidates = [NSMutableArray array];
        _pageSize = 0;
        _pageNumber = 0;
        _hasNextPage = NO;
        _hasPreviousPage = NO;
    }
    return self;
}

- (instancetype)initWithMenu:(RimeMenu *)menu {
    self = [super init];
    if (self) {
        _candidates = [NSMutableArray array];
        
        // 复制候选词列表
        if (menu.candidates) {
            for (RimeCandidate *candidate in menu.candidates) {
                if (candidate) {
                    [_candidates addObject:candidate];
                }
            }
        }
        
        _pageSize = menu.pageSize;
        _pageNumber = menu.pageNo;
        _hasNextPage = !menu.isLastPage; // false表示不是最后一页
        _hasPreviousPage = menu.pageNo > 0;
    }
    return self;
}

- (NSInteger)count {
    return self.candidates.count;
}

- (BOOL)isEmpty {
    return self.candidates.count == 0;
}

- (nullable RimeCandidate *)candidateAtIndex:(NSInteger)index {
    if (index >= 0 && index < self.candidates.count) {
        return self.candidates[index];
    }
    return nil;
}

- (nullable NSString *)textAtIndex:(NSInteger)index {
    RimeCandidate *candidate = [self candidateAtIndex:index];
    return candidate ? candidate.text : nil;
}

- (nullable NSString *)commentAtIndex:(NSInteger)index {
    RimeCandidate *candidate = [self candidateAtIndex:index];
    return candidate ? candidate.comment : nil;
}

- (NSArray<NSString *> *)allTexts {
    NSMutableArray<NSString *> *textList = [NSMutableArray array];
    for (RimeCandidate *candidate in self.candidates) {
        if (candidate && candidate.text) {
            [textList addObject:candidate.text];
        }
    }
    return [textList copy];
}

- (NSArray<RimeCandidate *> *)allCandidates {
    return [self.candidates copy];
}

- (NSInteger)indexOfText:(NSString *)text {
    for (NSInteger i = 0; i < self.candidates.count; i++) {
        RimeCandidate *candidate = self.candidates[i];
        if (candidate && [text isEqualToString:candidate.text]) {
            return i;
        }
    }
    return -1;
}

- (BOOL)isValidIndex:(NSInteger)index {
    return index >= 0 && index < self.candidates.count;
}

- (nullable NSString *)displayTextAtIndex:(NSInteger)index showComment:(BOOL)showComment {
    RimeCandidate *candidate = [self candidateAtIndex:index];
    if (!candidate || !candidate.text) {
        return nil;
    }
    
    if (showComment && candidate.comment && candidate.comment.length > 0) {
        return [NSString stringWithFormat:@"%@ %@", candidate.text, candidate.comment];
    } else {
        return candidate.text;
    }
}

- (NSString *)labelAtIndex:(NSInteger)index {
    // 使用1-9, 0的序号系统
    if (index >= 0 && index < 9) {
        return [NSString stringWithFormat:@"%ld", (long)(index + 1)];
    } else if (index == 9) {
        return @"0";
    } else {
        return [NSString stringWithFormat:@"%c", (char)('a' + index - 10)]; // 超过10个用字母
    }
}

- (NSString *)description {
    NSMutableString *desc = [NSMutableString string];
    [desc appendFormat:@"RimeCandidateList{size=%ld, page=%ld, pageSize=%ld, hasNext=%@, hasPrevious=%@, candidates=[",
        (long)self.candidates.count, (long)self.pageNumber, (long)self.pageSize,
        self.hasNextPage ? @"YES" : @"NO", self.hasPreviousPage ? @"YES" : @"NO"];
    
    NSInteger displayCount = MIN(self.candidates.count, 3);
    for (NSInteger i = 0; i < displayCount; i++) {
        if (i > 0) [desc appendString:@", "];
        RimeCandidate *candidate = self.candidates[i];
        [desc appendString:candidate ? candidate.text : @"null"];
    }
    
    if (self.candidates.count > 3) {
        [desc appendString:@", ..."];
    }
    
    [desc appendString:@"]}"];
    return desc;
}

@end