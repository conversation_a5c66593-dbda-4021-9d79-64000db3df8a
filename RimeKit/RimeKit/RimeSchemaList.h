#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 输入方案列表项
 */
@interface RimeSchemaListItem : NSObject

@property (nonatomic, copy, readonly) NSString *schemaId;
@property (nonatomic, copy, readonly) NSString *name;

- (instancetype)initWithSchemaId:(NSString *)schemaId name:(NSString *)name;

@end

/**
 * Rime输入方案列表
 * 对应librime的RimeSchemaList结构体
 */
@interface RimeSchemaList : NSObject

@property (nonatomic, copy, readonly) NSArray<RimeSchemaListItem *> *schemas;

- (instancetype)initWithSchemas:(NSArray<RimeSchemaListItem *> *)schemas;

@end

NS_ASSUME_NONNULL_END