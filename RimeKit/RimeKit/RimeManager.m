#import "RimeManager.h"
#import "RimeInputManager.h"
#import "RimeInputSession.h"
#import "RimeKeyboardEvent.h"
#import "RimeCandidateList.h"
#import "RimeComposition.h"
#import "RimeEngine.h"
#import "RimeTraits.h"
#import "RimeInputMode.h"
#import <UIKit/UIKit.h>

@interface RimeManager () <RimeInputSessionDelegate>

@property (nonatomic, strong) RimeInputManager *inputManager;
@property (nonatomic, strong) RimeInputSession *session;
@property (nonatomic, weak) id<RimeManagerDelegate> delegate;
@property (nonatomic, assign) BOOL isInitialized;

@end

@implementation RimeManager

#pragma mark - Singleton

+ (instancetype)sharedManager {
    static RimeManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _isInitialized = NO;
    }
    return self;
}

#pragma mark - Public Methods

- (BOOL)initializeEngine {
    if (self.isInitialized) {
        NSLog(@"[RimeManager] 已经初始化");
        return YES;
    }
    
    @try {
        // 获取输入管理器单例
        self.inputManager = [RimeInputManager sharedManager];
        
        // 初始化底层引擎
        if (![self.inputManager initialize]) {
            NSLog(@"[RimeManager] 底层引擎初始化失败");
            return NO;
        }
        
        // 创建会话
        self.session = [self.inputManager createSession];
        if (!self.session) {
            NSLog(@"[RimeManager] 创建会话失败");
            return NO;
        }
        
        // 设置会话监听器
        self.session.delegate = self;
        
        // 选择默认输入方案（拼音）
        [self switchInputMode:RimeInputModePinyin];
        
        self.isInitialized = YES;
        NSLog(@"[RimeManager] 初始化成功");
        return YES;
        
    } @catch (NSException *exception) {
        NSLog(@"[RimeManager] 初始化异常: %@", exception);
        return NO;
    }
}

- (nullable RimeCandidateList *)inputKey:(RimeKeyboardEvent *)keyEvent {
    if (!self.isInitialized) {
        NSLog(@"[RimeManager] RimeManager未初始化");
        return nil;
    }
    
    if (!self.session) {
        NSLog(@"[RimeManager] 会话为空");
        return nil;
    }
    
    // 使用 inputKey 方法同步返回候选词列表
    return [self.session inputKey:keyEvent];
}

- (void)setImeCallback:(nullable id<RimeManagerDelegate>)delegate {
    self.delegate = delegate;
}

- (nullable RimeCandidateList *)pageUp {
    if (!self.isInitialized || !self.session) {
        NSLog(@"[RimeManager] RimeManager未初始化或会话为空");
        return nil;
    }
    
    @try {
        return [self.session pageUp];
    } @catch (NSException *exception) {
        NSLog(@"[RimeManager] 翻页失败: %@", exception);
        return nil;
    }
}

- (nullable RimeCandidateList *)pageDown {
    if (!self.isInitialized || !self.session) {
        NSLog(@"[RimeManager] RimeManager未初始化或会话为空");
        return nil;
    }
    
    @try {
        return [self.session pageDown];
    } @catch (NSException *exception) {
        NSLog(@"[RimeManager] 翻页失败: %@", exception);
        return nil;
    }
}

- (void)reset {
    if (!self.isInitialized) {
        NSLog(@"[RimeManager] RimeManager未初始化");
        return;
    }
    
    @try {
        // 关闭当前会话
        if (self.session) {
            [self.session close];
            self.session = nil;
        }
        
        // 创建新会话
        self.session = [self.inputManager createSession];
        if (!self.session) {
            NSLog(@"[RimeManager] 重置会话失败：无法创建新会话");
            return;
        }
        
        // 重新设置监听器
        self.session.delegate = self;
        
        // 重新选择默认输入方案（拼音）
        [self switchInputMode:RimeInputModePinyin];
        
        NSLog(@"[RimeManager] 会话重置成功");
        
        // 通知回调，触发候选词更新
        if (self.delegate && [self.delegate respondsToSelector:@selector(onUpdateCandidateWords:)]) {
            RimeCandidateList *emptyCandidates = [[RimeCandidateList alloc] init];
            [self.delegate onUpdateCandidateWords:emptyCandidates];
        }
        
    } @catch (NSException *exception) {
        NSLog(@"[RimeManager] 重置会话失败: %@", exception);
    }
}

- (BOOL)switchInputMode:(RimeInputMode)inputMode {
    NSString *schemaId = [RimeInputModeHelper schemaIdForInputMode:inputMode];
    return [self switchSchema:schemaId];
}

- (BOOL)switchInputModeWithValue:(NSInteger)modeValue {
    RimeInputMode inputMode;
    if (![RimeInputModeHelper inputModeFromValue:modeValue inputMode:&inputMode]) {
        NSLog(@"[RimeManager] 无效的输入模式值: %ld", (long)modeValue);
        return NO;
    }
    return [self switchInputMode:inputMode];
}

- (BOOL)switchSchema:(NSString *)schemaId {
    if (!self.isInitialized || !self.session) {
        NSLog(@"[RimeManager] RimeManager未初始化或会话为空");
        return NO;
    }
    
    @try {
        // 切换输入方案
        BOOL success = [self.session selectSchema:schemaId];
        
        if (success) {
            NSLog(@"[RimeManager] 输入方案切换成功：%@", schemaId);
            
            // 清空当前输入状态
            [self.session clearComposition];
            
            // 通知回调，触发候选词更新
            if (self.delegate && [self.delegate respondsToSelector:@selector(onUpdateCandidateWords:)]) {
                RimeCandidateList *emptyCandidates = [[RimeCandidateList alloc] init];
                [self.delegate onUpdateCandidateWords:emptyCandidates];
            }
        } else {
            NSLog(@"[RimeManager] 输入方案切换失败：%@", schemaId);
        }
        
        return success;
        
    } @catch (NSException *exception) {
        NSLog(@"[RimeManager] 切换输入方案异常: %@", exception);
        return NO;
    }
}

#pragma mark - RimeInputSessionDelegate

- (void)onTextCommit:(NSString *)text {
    // 根据接口设计，delegate 没有提交文本的方法
    // 这里只能记录日志，无法通知应用
    NSLog(@"[RimeManager] 文本提交: %@", text);
}

- (void)onCandidatesUpdate:(RimeCandidateList *)candidates {
    // 当候选词更新时，调用回调
    if (self.delegate && [self.delegate respondsToSelector:@selector(onUpdateCandidateWords:)]) {
        [self.delegate onUpdateCandidateWords:candidates];
    }
}

- (void)onCompositionUpdate:(nullable RimeComposition *)composition {
    // 根据接口设计，delegate 没有编辑状态更新的方法
    // 这里只能记录日志，无法通知应用
    NSString *composingText = composition ? composition.preedit : @"";
    NSLog(@"[RimeManager] 编辑状态更新: %@", composingText);
}

@end