//
//  RimeKitResourceLoader.m
//  RimeKit
//
//  资源加载器实现
//

#import "RimeKitResourceLoader.h"
#import "RimeEngine.h"

@implementation RimeKitResourceLoader

+ (nullable NSBundle *)resourceBundle {
    // 1. 首先尝试从framework bundle中查找
    NSBundle *frameworkBundle = [NSBundle bundleForClass:[RimeEngine class]];
    NSURL *resourceURL = [frameworkBundle URLForResource:@"RimeKitResources" withExtension:@"bundle"];
    
    if (resourceURL) {
        NSBundle *resourceBundle = [NSBundle bundleWithURL:resourceURL];
        if (resourceBundle) {
            NSLog(@"[RimeKitResourceLoader] ✅ 从Framework中找到资源Bundle");
            return resourceBundle;
        }
    }
    
    // 2. 尝试从主Bundle的Frameworks目录查找
    NSBundle *mainBundle = [NSBundle mainBundle];
    NSString *frameworksPath = [mainBundle.bundlePath stringByAppendingPathComponent:@"Frameworks"];
    NSArray *frameworks = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:frameworksPath error:nil];
    
    for (NSString *framework in frameworks) {
        if ([framework hasPrefix:@"RimeKit"]) {
            NSString *rimeKitPath = [frameworksPath stringByAppendingPathComponent:framework];
            NSString *resourcesPath = [rimeKitPath stringByAppendingPathComponent:@"Resources"];
            NSString *bundlePath = [resourcesPath stringByAppendingPathComponent:@"RimeKitResources.bundle"];
            
            if ([[NSFileManager defaultManager] fileExistsAtPath:bundlePath]) {
                NSLog(@"[RimeKitResourceLoader] ✅ 从Frameworks目录找到资源Bundle");
                return [NSBundle bundleWithPath:bundlePath];
            }
        }
    }
    
    // 3. 尝试直接从主Bundle查找
    NSString *bundlePath = [mainBundle pathForResource:@"RimeKitResources" ofType:@"bundle"];
    if (bundlePath) {
        NSLog(@"[RimeKitResourceLoader] ✅ 从主Bundle找到资源Bundle");
        return [NSBundle bundleWithPath:bundlePath];
    }
    
    NSLog(@"[RimeKitResourceLoader] ⚠️ 无法找到RimeKitResources.bundle");
    return nil;
}

+ (nullable NSString *)prebuiltDataPath {
    NSBundle *resourceBundle = [self resourceBundle];
    if (!resourceBundle) {
        return nil;
    }
    
    NSString *path = [resourceBundle pathForResource:@"prebuilt" ofType:nil];
    if (path && [[NSFileManager defaultManager] fileExistsAtPath:path]) {
        return path;
    }
    
    return nil;
}

+ (nullable NSString *)rimeDataPath {
    NSBundle *resourceBundle = [self resourceBundle];
    if (!resourceBundle) {
        return nil;
    }
    
    NSString *path = [resourceBundle pathForResource:@"rime-data" ofType:nil];
    if (path && [[NSFileManager defaultManager] fileExistsAtPath:path]) {
        return path;
    }
    
    return nil;
}

+ (BOOL)copyResourcesToDirectory:(NSString *)destinationDir {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;
    
    // 创建目标目录
    if (![fileManager createDirectoryAtPath:destinationDir 
                withIntermediateDirectories:YES 
                                 attributes:nil 
                                      error:&error]) {
        NSLog(@"[RimeKitResourceLoader] ❌ 创建目标目录失败: %@", error);
        return NO;
    }
    
    // 获取资源路径
    NSString *prebuiltPath = [self prebuiltDataPath];
    NSString *rimeDataPath = [self rimeDataPath];
    
    if (!prebuiltPath && !rimeDataPath) {
        NSLog(@"[RimeKitResourceLoader] ❌ 未找到任何资源");
        return NO;
    }
    
    BOOL success = YES;
    
    // 复制prebuilt目录到build子目录
    if (prebuiltPath) {
        NSString *destBuildDir = [destinationDir stringByAppendingPathComponent:@"build"];
        if (![fileManager createDirectoryAtPath:destBuildDir 
                    withIntermediateDirectories:YES 
                                     attributes:nil 
                                          error:&error]) {
            NSLog(@"[RimeKitResourceLoader] ❌ 创建build目录失败: %@", error);
            success = NO;
        } else {
            NSArray *files = [fileManager contentsOfDirectoryAtPath:prebuiltPath error:&error];
            for (NSString *file in files) {
                NSString *srcFile = [prebuiltPath stringByAppendingPathComponent:file];
                NSString *dstFile = [destBuildDir stringByAppendingPathComponent:file];
                
                // 如果文件不存在则复制
                if (![fileManager fileExistsAtPath:dstFile]) {
                    if (![fileManager copyItemAtPath:srcFile toPath:dstFile error:&error]) {
                        NSLog(@"[RimeKitResourceLoader] ❌ 复制预编译文件失败 %@: %@", file, error);
                        success = NO;
                    } else {
                        NSLog(@"[RimeKitResourceLoader] ✅ 复制预编译文件: %@", file);
                    }
                }
            }
        }
    }
    
    // 复制rime-data文件
    if (rimeDataPath) {
        NSArray *files = [fileManager contentsOfDirectoryAtPath:rimeDataPath error:&error];
        for (NSString *file in files) {
            NSString *srcFile = [rimeDataPath stringByAppendingPathComponent:file];
            NSString *dstFile = [destinationDir stringByAppendingPathComponent:file];
            
            // 如果文件不存在则复制
            if (![fileManager fileExistsAtPath:dstFile]) {
                if (![fileManager copyItemAtPath:srcFile toPath:dstFile error:&error]) {
                    NSLog(@"[RimeKitResourceLoader] ❌ 复制配置文件失败 %@: %@", file, error);
                    success = NO;
                } else {
                    NSLog(@"[RimeKitResourceLoader] ✅ 复制配置文件: %@", file);
                }
            }
        }
    }
    
    return success;
}

+ (BOOL)checkResourcesAvailable {
    NSBundle *resourceBundle = [self resourceBundle];
    if (!resourceBundle) {
        return NO;
    }
    
    // 检查关键文件是否存在
    NSArray *requiredFiles = @[
        @"prebuilt/rime_ice.prism.bin",
        @"prebuilt/rime_ice.table.bin",
        @"prebuilt/rime_ice.reverse.bin",
        @"prebuilt/t9.prism.bin",
        @"prebuilt/prebuild_info.yaml",
        @"rime-data/default.yaml",
        @"rime-data/rime_ice.schema.yaml",
        @"rime-data/rime_ice.dict.yaml",
        @"rime-data/symbols_v.yaml",
        @"rime-data/t9.schema.yaml"
    ];
    
    for (NSString *filePath in requiredFiles) {
        NSArray *components = [filePath componentsSeparatedByString:@"/"];
        NSString *dir = components[0];
        NSString *file = components[1];
        
        NSString *dirPath = [resourceBundle pathForResource:dir ofType:nil];
        if (!dirPath) {
            NSLog(@"[RimeKitResourceLoader] ⚠️ 缺少目录: %@", dir);
            return NO;
        }
        
        NSString *fullPath = [dirPath stringByAppendingPathComponent:file];
        if (![[NSFileManager defaultManager] fileExistsAtPath:fullPath]) {
            NSLog(@"[RimeKitResourceLoader] ⚠️ 缺少文件: %@", filePath);
            return NO;
        }
    }
    
    NSLog(@"[RimeKitResourceLoader] ✅ 所有必需资源都可用");
    return YES;
}

+ (nullable NSString *)pathForConfigFile:(NSString *)fileName {
    NSString *rimeDataPath = [self rimeDataPath];
    if (!rimeDataPath) {
        return nil;
    }
    
    NSString *filePath = [rimeDataPath stringByAppendingPathComponent:fileName];
    if ([[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
        return filePath;
    }
    
    return nil;
}

+ (nullable NSString *)pathForPrebuiltFile:(NSString *)fileName {
    NSString *prebuiltPath = [self prebuiltDataPath];
    if (!prebuiltPath) {
        return nil;
    }
    
    NSString *filePath = [prebuiltPath stringByAppendingPathComponent:fileName];
    if ([[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
        return filePath;
    }
    
    return nil;
}

@end
