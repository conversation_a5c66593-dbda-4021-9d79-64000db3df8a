#import "RimeInputSession.h"
#import "RimeKeyboardEvent.h"
#import "RimeCandidateList.h"
#import "RimeContext.h"
#import "RimeCommit.h"
#import "RimeComposition.h"
#import "RimeMenu.h"
#import "RimeCandidate.h"

@interface RimeInputSession ()
@property (nonatomic, strong) RimeEngine *engine;
@property (nonatomic, assign) RimeSessionId sessionId;
@property (nonatomic, assign) BOOL isActive;
@end

@implementation RimeInputSession

- (instancetype)initWithSessionId:(RimeSessionId)sessionId engine:(RimeEngine *)engine {
    self = [super init];
    if (self) {
        _sessionId = sessionId;
        _engine = engine;
        _isActive = YES;
    }
    return self;
}

- (void)dealloc {
    if (_isActive) {
        [self close];
    }
}

#pragma mark - 输入处理

- (BOOL)processCharacter:(char)character {
    [self checkActive];
    
    @try {
        // 直接使用原始字符作为按键码，不进行大小写转换
        int keyCode = (int)character;
        BOOL processed = [self.engine processKey:self.sessionId keyCode:keyCode mask:0];
        
        if (processed) {
            [self updateContext];
        }
        
        return processed;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error processing character: %c, %@", character, exception);
        return NO;
    }
}

- (BOOL)processKey:(RimeKeyboardEvent *)keyEvent {
    [self checkActive];
    
    @try {
        BOOL processed = [self.engine processKey:self.sessionId keyCode:keyEvent.keyCode mask:keyEvent.modifiers];
        
        if (processed) {
            [self updateContext];
        }
        
        return processed;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error processing key: %@, %@", keyEvent, exception);
        return NO;
    }
}

- (nullable RimeCandidateList *)inputKey:(RimeKeyboardEvent *)keyEvent {
    [self checkActive];
    
    @try {
        BOOL processed = [self.engine processKey:self.sessionId keyCode:keyEvent.keyCode mask:keyEvent.modifiers];
        
        if (processed) {
            // 获取上下文信息
            NSDictionary *rimeContext = [self.engine getContext:self.sessionId];
            if (rimeContext) {
                // 从字典中提取组合信息
                NSDictionary *composition = rimeContext[@"composition"];
                if (composition && [self.delegate respondsToSelector:@selector(onCompositionUpdate:)]) {
                    // 创建一个简单的组合对象来传递给委托
                    RimeComposition *comp = [[RimeComposition alloc] initWithLength:[composition[@"length"] intValue]
                                                                          cursorPos:[composition[@"cursorPos"] intValue]
                                                                           selStart:[composition[@"selStart"] intValue]
                                                                             selEnd:[composition[@"selEnd"] intValue]
                                                                            preedit:composition[@"preedit"] ?: @""];
                    [self.delegate onCompositionUpdate:comp];
                }
                
                // 检查是否有文本需要提交
                [self checkForCommit];
                
                // 获取并返回候选词列表
                NSDictionary *menu = rimeContext[@"menu"];
                if (menu) {
                    NSArray *candidatesArray = menu[@"candidates"];
                    if (candidatesArray.count > 0) {
                        // 创建候选词列表
                        NSMutableArray<RimeCandidate *> *candidates = [NSMutableArray array];
                        for (NSDictionary *candidateDict in candidatesArray) {
                            RimeCandidate *candidate = [[RimeCandidate alloc] initWithText:candidateDict[@"text"] ?: @""
                                                                                   comment:candidateDict[@"comment"]];
                            [candidates addObject:candidate];
                        }
                        
                        // 创建菜单对象
                        RimeMenu *rimeMenu = [[RimeMenu alloc] initWithPageSize:[menu[@"pageSize"] intValue]
                                                                         pageNo:[menu[@"pageNo"] intValue]
                                                                     isLastPage:[menu[@"isLastPage"] boolValue]
                                                         highlightedCandidateIndex:[menu[@"highlightedCandidateIndex"] intValue]
                                                                     candidates:candidates
                                                                     selectKeys:menu[@"selectKeys"]];
                        
                        RimeCandidateList *candidateList = [[RimeCandidateList alloc] initWithMenu:rimeMenu];
                        
                        // 通知委托候选词更新
                        if ([self.delegate respondsToSelector:@selector(onCandidatesUpdate:)]) {
                            [self.delegate onCandidatesUpdate:candidateList];
                        }
                        
                        return candidateList;
                    }
                }
            }
        }
        
        return nil;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error processing key: %@, %@", keyEvent, exception);
        return nil;
    }
}

#pragma mark - 候选词操作

- (BOOL)selectCandidate:(NSInteger)index {
    [self checkActive];
    
    @try {
        BOOL selected = [self.engine selectCandidateOnCurrentPage:self.sessionId index:(int)index];
        
        if (selected) {
            [self updateContext];
            [self checkForCommit];
        }
        
        return selected;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error selecting candidate at index: %ld, %@", (long)index, exception);
        return NO;
    }
}

- (BOOL)deleteCandidate:(NSInteger)index {
    [self checkActive];
    
    @try {
        BOOL deleted = [self.engine deleteCandidate:self.sessionId index:(int)index];
        
        if (deleted) {
            [self updateContext];
        }
        
        return deleted;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error deleting candidate at index: %ld, %@", (long)index, exception);
        return NO;
    }
}

- (BOOL)changePage:(BOOL)backward {
    [self checkActive];
    
    @try {
        BOOL changed = [self.engine changePage:self.sessionId backward:backward];
        
        if (changed) {
            [self updateContext];
        }
        
        return changed;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error changing page, backward: %@, %@", backward ? @"YES" : @"NO", exception);
        return NO;
    }
}

- (nullable RimeCandidateList *)changePageAndGetCandidates:(BOOL)backward {
    [self checkActive];
    
    @try {
        BOOL changed = [self.engine changePage:self.sessionId backward:backward];
        
        if (changed) {
            // 获取上下文信息
            NSDictionary *rimeContext = [self.engine getContext:self.sessionId];
            if (rimeContext) {
                // 处理组合信息
                NSDictionary *composition = rimeContext[@"composition"];
                if (composition && [self.delegate respondsToSelector:@selector(onCompositionUpdate:)]) {
                    RimeComposition *comp = [[RimeComposition alloc] initWithLength:[composition[@"length"] intValue]
                                                                          cursorPos:[composition[@"cursorPos"] intValue]
                                                                           selStart:[composition[@"selStart"] intValue]
                                                                             selEnd:[composition[@"selEnd"] intValue]
                                                                            preedit:composition[@"preedit"] ?: @""];
                    [self.delegate onCompositionUpdate:comp];
                }
                
                // 获取并返回候选词列表
                NSDictionary *menu = rimeContext[@"menu"];
                if (menu) {
                    NSArray *candidatesArray = menu[@"candidates"];
                    if (candidatesArray.count > 0) {
                        NSMutableArray<RimeCandidate *> *candidates = [NSMutableArray array];
                        for (NSDictionary *candidateDict in candidatesArray) {
                            RimeCandidate *candidate = [[RimeCandidate alloc] initWithText:candidateDict[@"text"] ?: @""
                                                                                   comment:candidateDict[@"comment"]];
                            [candidates addObject:candidate];
                        }
                        
                        RimeMenu *rimeMenu = [[RimeMenu alloc] initWithPageSize:[menu[@"pageSize"] intValue]
                                                                         pageNo:[menu[@"pageNo"] intValue]
                                                                     isLastPage:[menu[@"isLastPage"] boolValue]
                                                         highlightedCandidateIndex:[menu[@"highlightedCandidateIndex"] intValue]
                                                                     candidates:candidates
                                                                     selectKeys:menu[@"selectKeys"]];
                        
                        RimeCandidateList *candidateList = [[RimeCandidateList alloc] initWithMenu:rimeMenu];
                        // 同时通知委托候选词更新
                        if ([self.delegate respondsToSelector:@selector(onCandidatesUpdate:)]) {
                            [self.delegate onCandidatesUpdate:candidateList];
                        }
                        return candidateList;
                    }
                }
            }
        }
        
        return nil;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error changing page, backward: %@, %@", backward ? @"YES" : @"NO", exception);
        return nil;
    }
}

- (nullable RimeCandidateList *)pageUp {
    return [self changePageAndGetCandidates:YES];
}

- (nullable RimeCandidateList *)pageDown {
    return [self changePageAndGetCandidates:NO];
}

#pragma mark - 组合操作

- (BOOL)commitComposition {
    [self checkActive];
    
    @try {
        BOOL committed = [self.engine commitComposition:self.sessionId];
        
        if (committed) {
            [self checkForCommit];
            [self updateContext];
        }
        
        return committed;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error committing composition: %@", exception);
        return NO;
    }
}

- (void)clearComposition {
    [self checkActive];
    
    @try {
        [self.engine clearComposition:self.sessionId];
        [self updateContext];
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error clearing composition: %@", exception);
    }
}

#pragma mark - 输入管理

- (nullable NSString *)currentInput {
    [self checkActive];
    return [self.engine getInput:self.sessionId];
}

- (BOOL)setInput:(NSString *)input {
    [self checkActive];
    
    @try {
        BOOL result = [self.engine setInput:self.sessionId input:input];
        if (result) {
            [self updateContext];
        }
        return result;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error setting input: %@, %@", input, exception);
        return NO;
    }
}

- (nullable RimeCandidateList *)candidates {
    [self checkActive];
    
    @try {
        NSDictionary *context = [self.engine getContext:self.sessionId];
        NSLog(@"[RimeInputSession] Context: %@", context);
        if (context) {
            NSDictionary *menu = context[@"menu"];
            if (menu) {
                NSArray *candidatesArray = menu[@"candidates"];
                if (candidatesArray.count > 0) {
                    NSMutableArray<RimeCandidate *> *candidates = [NSMutableArray array];
                    for (NSDictionary *candidateDict in candidatesArray) {
                        RimeCandidate *candidate = [[RimeCandidate alloc] initWithText:candidateDict[@"text"] ?: @""
                                                                               comment:candidateDict[@"comment"]];
                        [candidates addObject:candidate];
                    }
                    
                    RimeMenu *rimeMenu = [[RimeMenu alloc] initWithPageSize:[menu[@"pageSize"] intValue]
                                                                     pageNo:[menu[@"pageNo"] intValue]
                                                                 isLastPage:[menu[@"isLastPage"] boolValue]
                                                     highlightedCandidateIndex:[menu[@"highlightedCandidateIndex"] intValue]
                                                                 candidates:candidates
                                                                 selectKeys:menu[@"selectKeys"]];
                    
                    return [[RimeCandidateList alloc] initWithMenu:rimeMenu];
                }
            }
        }
        return nil;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error getting candidates: %@", exception);
        return nil;
    }
}

#pragma mark - 方案管理

- (BOOL)selectSchema:(NSString *)schemaId {
    [self checkActive];
    
    @try {
        BOOL selected = [self.engine selectSchema:self.sessionId schemaId:schemaId];
        if (selected) {
            [self updateContext];
        }
        return selected;
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error selecting schema: %@, %@", schemaId, exception);
        return NO;
    }
}

- (nullable NSString *)currentSchema {
    [self checkActive];
    
    @try {
        return [self.engine getCurrentSchema:self.sessionId];
    } @catch (NSException *exception) {
        NSLog(@"[RimeInputSession] Error getting current schema: %@", exception);
        return nil;
    }
}

#pragma mark - 生命周期

- (void)close {
    if (self.isActive) {
        self.isActive = NO;
        @try {
            [self.engine destroySession:self.sessionId];
            NSLog(@"[RimeInputSession] Session closed: %lu", (unsigned long)self.sessionId);
        } @catch (NSException *exception) {
            NSLog(@"[RimeInputSession] Error closing session: %lu, %@", (unsigned long)self.sessionId, exception);
        }
    }
}

#pragma mark - Private Methods

- (void)checkActive {
    if (!self.isActive) {
        [NSException raise:NSInternalInconsistencyException 
                    format:@"InputSession is not active. It may have been closed."];
    }
}

- (void)updateContext {
    [self checkActive];
    NSDictionary *rimeContext = [self.engine getContext:self.sessionId];
    if (self.delegate && rimeContext) {
        // 处理组合信息
        NSDictionary *composition = rimeContext[@"composition"];
        if (composition && [self.delegate respondsToSelector:@selector(onCompositionUpdate:)]) {
            RimeComposition *comp = [[RimeComposition alloc] initWithLength:[composition[@"length"] intValue]
                                                                  cursorPos:[composition[@"cursorPos"] intValue]
                                                                   selStart:[composition[@"selStart"] intValue]
                                                                     selEnd:[composition[@"selEnd"] intValue]
                                                                    preedit:composition[@"preedit"] ?: @""];
            [self.delegate onCompositionUpdate:comp];
        }
        
        // 处理候选词菜单
        NSDictionary *menu = rimeContext[@"menu"];
        if (menu && [self.delegate respondsToSelector:@selector(onCandidatesUpdate:)]) {
            NSArray *candidatesArray = menu[@"candidates"];
            if (candidatesArray.count > 0) {
                NSMutableArray<RimeCandidate *> *candidates = [NSMutableArray array];
                for (NSDictionary *candidateDict in candidatesArray) {
                    RimeCandidate *candidate = [[RimeCandidate alloc] initWithText:candidateDict[@"text"] ?: @""
                                                                           comment:candidateDict[@"comment"]];
                    [candidates addObject:candidate];
                }
                
                RimeMenu *rimeMenu = [[RimeMenu alloc] initWithPageSize:[menu[@"pageSize"] intValue]
                                                                 pageNo:[menu[@"pageNo"] intValue]
                                                             isLastPage:[menu[@"isLastPage"] boolValue]
                                                 highlightedCandidateIndex:[menu[@"highlightedCandidateIndex"] intValue]
                                                             candidates:candidates
                                                             selectKeys:menu[@"selectKeys"]];
                
                RimeCandidateList *candidateList = [[RimeCandidateList alloc] initWithMenu:rimeMenu];
                [self.delegate onCandidatesUpdate:candidateList];
            }
        }
    }
}

- (void)checkForCommit {
    [self checkActive];
    NSString *commitText = [self.engine getCommitText:self.sessionId];
    if (self.delegate && commitText && commitText.length > 0) {
        if ([self.delegate respondsToSelector:@selector(onTextCommit:)]) {
            [self.delegate onTextCommit:commitText];
        }
    }
}

@end