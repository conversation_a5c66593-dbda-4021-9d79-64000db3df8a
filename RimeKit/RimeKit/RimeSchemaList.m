#import "RimeSchemaList.h"

@implementation RimeSchemaListItem

- (instancetype)initWithSchemaId:(NSString *)schemaId name:(NSString *)name {
    self = [super init];
    if (self) {
        _schemaId = [schemaId copy];
        _name = [name copy];
    }
    return self;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"RimeSchemaListItem{schemaId='%@', name='%@'}", self.schemaId, self.name];
}

@end

@implementation RimeSchemaList

- (instancetype)initWithSchemas:(NSArray<RimeSchemaListItem *> *)schemas {
    self = [super init];
    if (self) {
        _schemas = [schemas copy];
    }
    return self;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"RimeSchemaList{schemas.count=%lu}", (unsigned long)self.schemas.count];
}

@end