#import "RimeInputMode.h"

@implementation RimeInputModeHelper

+ (NSString *)schemaIdForInputMode:(RimeInputMode)inputMode {
    switch (inputMode) {
        case RimeInputModePinyin:
            return @"clover";
        case RimeInputModeT9:
            return @"clover_jiugong";
        default:
            return @"clover";
    }
}

+ (BOOL)inputModeFromValue:(NSInteger)value inputMode:(RimeInputMode *)inputMode {
    if (value == RimeInputModePinyin || value == RimeInputModeT9) {
        if (inputMode) {
            *inputMode = (RimeInputMode)value;
        }
        return YES;
    }
    return NO;
}

@end
