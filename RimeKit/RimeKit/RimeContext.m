#import "RimeContext.h"
#import "RimeComposition.h"
#import "RimeMenu.h"

@implementation RimeContext

- (instancetype)initWithComposition:(nullable RimeComposition *)composition
                               menu:(nullable RimeMenu *)menu
                  commitTextPreview:(nullable NSString *)commitTextPreview
                       selectLabels:(NSArray<NSString *> *)selectLabels {
    self = [super init];
    if (self) {
        _composition = composition;
        _menu = menu;
        _commitTextPreview = [commitTextPreview copy];
        _selectLabels = [selectLabels copy];
    }
    return self;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"RimeContext{composition=%@, menu=%@, commitTextPreview='%@', selectLabels.count=%lu}",
            self.composition, self.menu, self.commitTextPreview ?: @"", (unsigned long)self.selectLabels.count];
}

@end