#import "RimeMenu.h"
#import "RimeCandidate.h"

@implementation RimeMenu

- (instancetype)initWithPageSize:(NSInteger)pageSize
                          pageNo:(NSInteger)pageNo
                      isLastPage:(BOOL)isLastPage
          highlightedCandidateIndex:(NSInteger)highlightedCandidateIndex
                      candidates:(NSArray<RimeCandidate *> *)candidates
                      selectKeys:(nullable NSString *)selectKeys {
    self = [super init];
    if (self) {
        _pageSize = pageSize;
        _pageNo = pageNo;
        _isLastPage = isLastPage;
        _highlightedCandidateIndex = highlightedCandidateIndex;
        _candidates = [candidates copy];
        _selectKeys = [selectKeys copy];
    }
    return self;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"RimeMenu{pageSize=%ld, pageNo=%ld, isLastPage=%@, highlightedCandidateIndex=%ld, candidates.count=%lu, selectKeys='%@'}",
            (long)self.pageSize, (long)self.pageNo,
            self.isLastPage ? @"YES" : @"NO",
            (long)self.highlightedCandidateIndex,
            (unsigned long)self.candidates.count,
            self.selectKeys ?: @""];
}

@end