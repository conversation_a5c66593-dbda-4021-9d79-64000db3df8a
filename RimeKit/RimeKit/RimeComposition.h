#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * Rime输入组合
 * 对应librime的RimeComposition结构体
 */
@interface RimeComposition : NSObject

@property (nonatomic, readonly) NSInteger length;
@property (nonatomic, readonly) NSInteger cursorPos;
@property (nonatomic, readonly) NSInteger selStart;
@property (nonatomic, readonly) NSInteger selEnd;
@property (nonatomic, copy, readonly) NSString *preedit;

- (instancetype)initWithLength:(NSInteger)length
                     cursorPos:(NSInteger)cursorPos
                      selStart:(NSInteger)selStart
                        selEnd:(NSInteger)selEnd
                       preedit:(NSString *)preedit;

@end

NS_ASSUME_NONNULL_END