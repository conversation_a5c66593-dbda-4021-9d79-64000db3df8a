//
//  RimeKitResourceLoader.h
//  RimeKit
//
//  资源加载器 - 用于访问RimeKit的资源文件
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface RimeKitResourceLoader : NSObject

/**
 获取RimeKit的资源Bundle
 @return 资源Bundle，如果未找到返回nil
 */
+ (nullable NSBundle *)resourceBundle;

/**
 获取预编译数据目录路径
 @return 预编译数据目录路径，如果未找到返回nil
 */
+ (nullable NSString *)prebuiltDataPath;

/**
 获取Rime配置数据目录路径
 @return Rime配置数据目录路径，如果未找到返回nil
 */
+ (nullable NSString *)rimeDataPath;

/**
 将资源文件复制到指定目录
 @param destinationDir 目标目录路径
 @return 是否成功复制
 */
+ (BOOL)copyResourcesToDirectory:(NSString *)destinationDir;

/**
 检查资源是否可用
 @return 资源是否可用
 */
+ (BOOL)checkResourcesAvailable;

/**
 获取指定的配置文件路径
 @param fileName 配置文件名（如 "default.yaml"）
 @return 配置文件路径，如果未找到返回nil
 */
+ (nullable NSString *)pathForConfigFile:(NSString *)fileName;

/**
 获取指定的预编译文件路径
 @param fileName 预编译文件名（如 "rime_ice.prism.bin"）
 @return 预编译文件路径，如果未找到返回nil
 */
+ (nullable NSString *)pathForPrebuiltFile:(NSString *)fileName;

@end

NS_ASSUME_NONNULL_END