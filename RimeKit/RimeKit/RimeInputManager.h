#import <Foundation/Foundation.h>

@class RimeInputSession;
@class RimeSchemaList;

NS_ASSUME_NONNULL_BEGIN

/**
 * Rime输入法SDK主要入口类
 * 提供简洁的API供应用内键盘集成使用
 * 专注于拼音输入功能
 */
@interface RimeInputManager : NSObject

/**
 * 获取SDK实例（单例模式）
 */
+ (instancetype)sharedManager;

/**
 * 初始化SDK
 * @return 初始化是否成功
 */
- (BOOL)initialize;

/**
 * 使用自定义配置初始化SDK
 * @param documentsPath 文档目录路径
 * @param bundleIdentifier 应用包标识符
 * @param version 版本号
 * @return 初始化是否成功
 */
- (BOOL)initializeWithDocumentsPath:(NSString *)documentsPath
                    bundleIdentifier:(NSString *)bundleIdentifier
                             version:(NSString *)version;

/**
 * 创建输入会话
 * @return 输入会话对象，失败返回nil
 */
- (nullable RimeInputSession *)createSession;

/**
 * 获取SDK版本信息
 * @return 版本字符串
 */
- (NSString *)version;

/**
 * 检查SDK是否已初始化
 */
@property (nonatomic, readonly) BOOL isInitialized;

/**
 * 销毁SDK，释放所有资源
 */
- (void)destroy;

/**
 * 执行维护任务（词典部署等）
 * @return 维护是否成功启动
 */
- (BOOL)performMaintenance;

/**
 * 检查是否正在维护中
 */
- (BOOL)isMaintenancing;

/**
 * 获取输入方案列表
 * @return 方案列表数组，如果未初始化或引擎返回nil则返回nil
 */
- (nullable NSArray *)schemaList;

@end

NS_ASSUME_NONNULL_END