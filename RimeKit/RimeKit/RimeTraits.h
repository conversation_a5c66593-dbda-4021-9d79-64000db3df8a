#import <Foundation/Foundation.h>

typedef uintptr_t RimeSessionId;

NS_ASSUME_NONNULL_BEGIN

/**
 * Rime引擎初始化配置参数
 * 对应librime的RimeTraits结构体
 */
@interface RimeTraits : NSObject

@property (nonatomic, copy) NSString *sharedDataDir;
@property (nonatomic, copy) NSString *userDataDir;
@property (nonatomic, copy) NSString *distributionName;
@property (nonatomic, copy) NSString *distributionCodeName;
@property (nonatomic, copy) NSString *distributionVersion;
@property (nonatomic, copy) NSString *appName;
@property (nonatomic, copy, nullable) NSArray<NSString *> *modules;
@property (nonatomic, assign) int minLogLevel; // 0=INFO, 1=WARNING, 2=ERROR, 3=FATAL
@property (nonatomic, copy, nullable) NSString *logDir;
@property (nonatomic, copy, nullable) NSString *prebuiltDataDir;
@property (nonatomic, copy, nullable) NSString *stagingDir;

/**
 * 初始化方法
 * @param sharedDataDir 共享数据目录
 * @param userDataDir 用户数据目录
 * @param distributionName 发行版名称
 * @param distributionCodeName 发行版代码名称
 * @param distributionVersion 发行版版本
 * @param appName 应用名称
 */
- (instancetype)initWithSharedDataDir:(NSString *)sharedDataDir
                         userDataDir:(NSString *)userDataDir
                    distributionName:(NSString *)distributionName
                distributionCodeName:(NSString *)distributionCodeName
                 distributionVersion:(NSString *)distributionVersion
                            appName:(NSString *)appName;

/**
 * 创建默认的iOS配置
 * @param bundleIdentifier 应用包标识符
 * @param documentsDir 文档目录
 * @param version 版本号
 * @return 默认配置
 */
+ (instancetype)defaultTraitsWithBundleIdentifier:(NSString *)bundleIdentifier
                                     documentsDir:(NSString *)documentsDir
                                          version:(NSString *)version;

@end

NS_ASSUME_NONNULL_END