#import "RimeStatus.h"

@implementation RimeStatus

- (instancetype)initWithSchemaId:(NSString *)schemaId
                      schemaName:(NSString *)schemaName
                      isDisabled:(BOOL)isDisabled
                     isComposing:(BOOL)isComposing
                     isAsciiMode:(BOOL)isAsciiMode
                     isFullShape:(BOOL)isFullShape
                    isSimplified:(BOOL)isSimplified
                   isTraditional:(BOOL)isTraditional
                    isAsciiPunct:(BOOL)isAsciiPunct {
    self = [super init];
    if (self) {
        _schemaId = [schemaId copy];
        _schemaName = [schemaName copy];
        _isDisabled = isDisabled;
        _isComposing = isComposing;
        _isAsciiMode = isAsciiMode;
        _isFullShape = isFullShape;
        _isSimplified = isSimplified;
        _isTraditional = isTraditional;
        _isAsciiPunct = isAsciiPunct;
    }
    return self;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"RimeStatus{schemaId='%@', schemaName='%@', isDisabled=%@, isComposing=%@, isAsciiMode=%@, isFullShape=%@, isSimplified=%@, isTraditional=%@, isAsciiPunct=%@}",
            self.schemaId, self.schemaName,
            self.isDisabled ? @"YES" : @"NO",
            self.isComposing ? @"YES" : @"NO",
            self.isAsciiMode ? @"YES" : @"NO",
            self.isFullShape ? @"YES" : @"NO",
            self.isSimplified ? @"YES" : @"NO",
            self.isTraditional ? @"YES" : @"NO",
            self.isAsciiPunct ? @"YES" : @"NO"];
}

@end