#import "RimeEngine.h"
#import "RimeTraits.h"
#import <string.h>

// 在包含rime_api.h之前，临时重定义以避免冲突
#define RimeTraits RimeTraits_C
#define RimeCommit RimeCommit_C
#define RimeStatus RimeStatus_C
#define RimeContext RimeContext_C
#define RimeCandidate RimeCandidate_C
#define RimeMenu RimeMenu_C
#define RimeComposition RimeComposition_C

#import "include/rime_api.h"

// 恢复定义
#undef RimeTraits
#undef RimeCommit
#undef RimeStatus
#undef RimeContext
#undef RimeCandidate
#undef RimeMenu
#undef RimeComposition

// 全局API指针
static RIME_FLAVORED(RimeApi) *rime = NULL;

// Rime通知处理函数
static void RimeEngineNotificationHandler(void *context_object, RimeSessionId session_id, 
                                         const char *message_type, const char *message_value) {
    NSLog(@"[RimeEngine] Notification: session=%lu, type=%s, value=%s", 
          (unsigned long)session_id, message_type, message_value);
}

@interface RimeEngine ()
@property (nonatomic, assign) BOOL isInitialized;
@property (nonatomic, strong) dispatch_queue_t engineQueue;
@end

@implementation RimeEngine

+ (instancetype)sharedEngine {
    static RimeEngine *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[RimeEngine alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _isInitialized = NO;
        _engineQueue = dispatch_queue_create("com.rimekit.engine", DISPATCH_QUEUE_SERIAL);
    }
    return self;
}

- (void)dealloc {
    if (_isInitialized) {
        [self finalize];
    }
}

#pragma mark - 生命周期管理

- (BOOL)initializeWithTraits:(RimeTraits *)traits {
    __block BOOL result = NO;
    
    dispatch_sync(self.engineQueue, ^{
        if (self.isInitialized) {
            NSLog(@"[RimeEngine] Rime engine is already initialized");
            result = YES;
            return;
        }
        
        NSLog(@"[RimeEngine] 开始初始化Rime引擎");
        
        // 获取Rime API
        if (!rime) {
            rime = RIME_FLAVORED(rime_get_api)();
            if (!rime) {
                NSLog(@"[RimeEngine] 无法获取Rime API");
                result = NO;
                return;
            }
        }
        
        // 准备数据目录
        NSFileManager *fileManager = [NSFileManager defaultManager];
        BOOL isDir;
        
        // 创建用户目录
        if (![fileManager fileExistsAtPath:traits.userDataDir isDirectory:&isDir]) {
            NSError *error;
            if (![fileManager createDirectoryAtPath:traits.userDataDir 
                        withIntermediateDirectories:YES 
                                         attributes:nil 
                                              error:&error]) {
                NSLog(@"[RimeEngine] 创建用户目录失败: %@", error);
                result = NO;
                return;
            }
        }
        
        // 准备共享数据目录
        if (![self prepareSharedDataDirectory:traits.sharedDataDir]) {
            NSLog(@"[RimeEngine] 准备共享数据目录失败");
            result = NO;
            return;
        }
        
        // 设置Rime特性 - 使用C结构体
        RIME_STRUCT(RimeTraits_C, rimeTraits);
        rimeTraits.shared_data_dir = traits.sharedDataDir.UTF8String;
        rimeTraits.user_data_dir = traits.userDataDir.UTF8String;
        rimeTraits.distribution_name = traits.distributionName.UTF8String;
        rimeTraits.distribution_code_name = traits.distributionCodeName.UTF8String;
        rimeTraits.distribution_version = traits.distributionVersion.UTF8String;
        rimeTraits.app_name = traits.appName.UTF8String;
        
        if (traits.logDir) {
            rimeTraits.log_dir = traits.logDir.UTF8String;
        }
        if (traits.prebuiltDataDir) {
            rimeTraits.prebuilt_data_dir = traits.prebuiltDataDir.UTF8String;
        }
        if (traits.stagingDir) {
            rimeTraits.staging_dir = traits.stagingDir.UTF8String;
        }
        
        rimeTraits.min_log_level = traits.minLogLevel;
        
        // 设置通知处理器
        rime->set_notification_handler(RimeEngineNotificationHandler, (__bridge void *)self);
        
        // 初始化
        rime->initialize(&rimeTraits);
        
        // 确保资源文件已复制
        NSString *sharedDataDir = traits.sharedDataDir;
        NSString *userDataDir = traits.userDataDir;
        NSString *buildDir = [userDataDir stringByAppendingPathComponent:@"build"];
        
        // 尝试从资源Bundle复制文件
        NSBundle *resourceBundle = [self findResourceBundle];
        if (resourceBundle) {
            NSLog(@"[RimeEngine] ✅ 找到资源Bundle，准备复制资源文件");
            [self copyResourcesFromBundle:resourceBundle toSharedDataDir:sharedDataDir buildDir:buildDir];
        } else {
            NSLog(@"[RimeEngine] ⚠️ 未找到资源Bundle，尝试其他位置");
            [self loadResourcesFromAlternativeLocations:sharedDataDir buildDir:buildDir];
        }
        
        // 强制执行完整部署以确保配置文件正确部署
        NSLog(@"[RimeEngine] 开始执行完整部署...");
        
        // 1. 首先运行预构建任务
        if (rime->prebuild()) {
            NSLog(@"[RimeEngine] ✅ 预构建任务完成");
        } else {
            NSLog(@"[RimeEngine] ⚠️ 预构建任务失败或跳过");
        }
        
        // 2. 执行完整的工作空间部署
        if (rime->deploy()) {
            NSLog(@"[RimeEngine] ✅ 工作空间部署完成");
        } else {
            NSLog(@"[RimeEngine] ❌ 工作空间部署失败");
        }
        
        // 3. 启动维护任务进行增量更新
        if (rime->start_maintenance(False)) {
            rime->join_maintenance_thread();
            NSLog(@"[RimeEngine] ✅ 维护任务完成");
        } else {
            NSLog(@"[RimeEngine] ⚠️ 维护任务失败或无需执行");
        }
        
        // 4. 验证关键配置文件是否存在
        [self verifyConfigurationFiles:buildDir sharedDataDir:sharedDataDir];
        
        // 5. 验证重要的子目录是否被正确复制
        [self verifyImportantDirectories:sharedDataDir];
        
        self.isInitialized = YES;
        result = YES;
        NSLog(@"[RimeEngine] Rime引擎初始化成功");
    });
    
    return result;
}

- (void)finalize {
    dispatch_sync(self.engineQueue, ^{
        if (self.isInitialized) {
            rime->finalize();
            self.isInitialized = NO;
            NSLog(@"[RimeEngine] Rime engine finalized");
        }
    });
}

#pragma mark - 会话管理

- (RimeSessionId)createSession {
    __block RimeSessionId sessionId = 0;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        sessionId = rime->create_session();
    });
    return sessionId;
}

- (BOOL)findSession:(RimeSessionId)sessionId {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->find_session(sessionId);
    });
    return result;
}

- (BOOL)destroySession:(RimeSessionId)sessionId {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->destroy_session(sessionId);
    });
    return result;
}

#pragma mark - 按键处理

- (BOOL)processKey:(RimeSessionId)sessionId keyCode:(int)keyCode mask:(int)mask {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->process_key(sessionId, keyCode, mask);
    });
    return result;
}

- (BOOL)commitComposition:(RimeSessionId)sessionId {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->commit_composition(sessionId);
    });
    return result;
}

- (void)clearComposition:(RimeSessionId)sessionId {
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        rime->clear_composition(sessionId);
    });
}

#pragma mark - 文本获取

- (nullable NSString *)getCommitText:(RimeSessionId)sessionId {
    __block NSString *commitText = nil;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        RIME_STRUCT(RimeCommit_C, rimeCommit);
        if (rime->get_commit(sessionId, &rimeCommit)) {
            if (rimeCommit.text) {
                commitText = [NSString stringWithUTF8String:rimeCommit.text];
            }
            rime->free_commit(&rimeCommit);
        }
    });
    return commitText;
}

- (nullable NSDictionary *)getContext:(RimeSessionId)sessionId {
    __block NSDictionary *context = nil;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        RIME_STRUCT(RimeContext_C, rimeContext);
        if (rime->get_context(sessionId, &rimeContext)) {
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            
            // 添加组合信息
            if (rimeContext.composition.length > 0) {
                NSMutableDictionary *composition = [NSMutableDictionary dictionary];
                composition[@"length"] = @(rimeContext.composition.length);
                composition[@"cursorPos"] = @(rimeContext.composition.cursor_pos);
                composition[@"selStart"] = @(rimeContext.composition.sel_start);
                composition[@"selEnd"] = @(rimeContext.composition.sel_end);
                if (rimeContext.composition.preedit) {
                    composition[@"preedit"] = [NSString stringWithUTF8String:rimeContext.composition.preedit];
                }
                dict[@"composition"] = composition;
            }
            
            // 添加菜单信息
            if (rimeContext.menu.num_candidates > 0) {
                NSMutableDictionary *menu = [NSMutableDictionary dictionary];
                menu[@"pageSize"] = @(rimeContext.menu.page_size);
                menu[@"pageNo"] = @(rimeContext.menu.page_no);
                menu[@"isLastPage"] = @(rimeContext.menu.is_last_page);
                menu[@"highlightedCandidateIndex"] = @(rimeContext.menu.highlighted_candidate_index);
                
                NSMutableArray *candidates = [NSMutableArray array];
                for (int i = 0; i < rimeContext.menu.num_candidates; i++) {
                    NSMutableDictionary *candidate = [NSMutableDictionary dictionary];
                    if (rimeContext.menu.candidates[i].text) {
                        candidate[@"text"] = [NSString stringWithUTF8String:rimeContext.menu.candidates[i].text];
                    }
                    if (rimeContext.menu.candidates[i].comment) {
                        candidate[@"comment"] = [NSString stringWithUTF8String:rimeContext.menu.candidates[i].comment];
                    }
                    [candidates addObject:candidate];
                }
                menu[@"candidates"] = candidates;
                
                if (rimeContext.menu.select_keys) {
                    menu[@"selectKeys"] = [NSString stringWithUTF8String:rimeContext.menu.select_keys];
                }
                
                dict[@"menu"] = menu;
            }
            
            // 添加提交预览文本
            if (RIME_STRUCT_HAS_MEMBER(rimeContext, rimeContext.commit_text_preview) && 
                rimeContext.commit_text_preview) {
                dict[@"commitTextPreview"] = [NSString stringWithUTF8String:rimeContext.commit_text_preview];
            }
            
            context = dict;
            rime->free_context(&rimeContext);
        }
    });
    return context;
}

- (nullable NSDictionary *)getStatus:(RimeSessionId)sessionId {
    __block NSDictionary *status = nil;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        RIME_STRUCT(RimeStatus_C, rimeStatus);
        if (rime->get_status(sessionId, &rimeStatus)) {
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            
            if (rimeStatus.schema_id) {
                dict[@"schemaId"] = [NSString stringWithUTF8String:rimeStatus.schema_id];
            }
            if (rimeStatus.schema_name) {
                dict[@"schemaName"] = [NSString stringWithUTF8String:rimeStatus.schema_name];
            }
            
            dict[@"isDisabled"] = @(rimeStatus.is_disabled);
            dict[@"isComposing"] = @(rimeStatus.is_composing);
            dict[@"isAsciiMode"] = @(rimeStatus.is_ascii_mode);
            dict[@"isFullShape"] = @(rimeStatus.is_full_shape);
            dict[@"isSimplified"] = @(rimeStatus.is_simplified);
            dict[@"isTraditional"] = @(rimeStatus.is_traditional);
            dict[@"isAsciiPunct"] = @(rimeStatus.is_ascii_punct);
            
            status = dict;
            rime->free_status(&rimeStatus);
        }
    });
    return status;
}

#pragma mark - 候选词操作

- (BOOL)selectCandidate:(RimeSessionId)sessionId index:(int)index {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->select_candidate(sessionId, index);
    });
    return result;
}

- (BOOL)selectCandidateOnCurrentPage:(RimeSessionId)sessionId index:(int)index {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->select_candidate_on_current_page(sessionId, index);
    });
    return result;
}

- (BOOL)deleteCandidate:(RimeSessionId)sessionId index:(int)index {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->delete_candidate(sessionId, index);
    });
    return result;
}

- (BOOL)changePage:(RimeSessionId)sessionId backward:(BOOL)backward {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->change_page(sessionId, backward);
    });
    return result;
}

#pragma mark - 配置和选项

- (void)setOption:(RimeSessionId)sessionId option:(NSString *)option value:(BOOL)value {
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        rime->set_option(sessionId, option.UTF8String, value);
    });
}

- (BOOL)getOption:(RimeSessionId)sessionId option:(NSString *)option {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->get_option(sessionId, option.UTF8String);
    });
    return result;
}

- (BOOL)selectSchema:(RimeSessionId)sessionId schemaId:(NSString *)schemaId {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        
        // 在选择方案前检查配置文件是否存在
        NSString *userDataDir = [NSString stringWithUTF8String:rime->get_user_data_dir()];
        NSString *buildDir = [userDataDir stringByAppendingPathComponent:@"build"];
        NSString *schemaFilePath = [buildDir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.schema.yaml", schemaId]];
        
        NSFileManager *fileManager = [NSFileManager defaultManager];
        if (![fileManager fileExistsAtPath:schemaFilePath]) {
            NSLog(@"[RimeEngine] ⚠️ 方案文件不存在: %@，尝试重新部署", schemaFilePath);
            
            // 尝试重新部署该方案
            if (rime->deploy_schema([NSString stringWithFormat:@"%@.schema.yaml", schemaId].UTF8String)) {
                NSLog(@"[RimeEngine] ✅ 方案重新部署成功: %@", schemaId);
            } else {
                NSLog(@"[RimeEngine] ❌ 方案重新部署失败: %@", schemaId);
            }
        }
        
        result = rime->select_schema(sessionId, schemaId.UTF8String);
        if (result) {
            NSLog(@"[RimeEngine] ✅ 成功选择方案: %@", schemaId);
        } else {
            NSLog(@"[RimeEngine] ❌ 选择方案失败: %@", schemaId);
        }
    });
    return result;
}

- (nullable NSString *)getCurrentSchema:(RimeSessionId)sessionId {
    __block NSString *schemaId = nil;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        char buffer[256];
        if (rime->get_current_schema(sessionId, buffer, sizeof(buffer))) {
            schemaId = [NSString stringWithUTF8String:buffer];
        }
    });
    return schemaId;
}

- (nullable NSArray *)getSchemaList {
    __block NSArray *schemaList = nil;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        RimeSchemaList rimeSchemaList;
        if (rime->get_schema_list(&rimeSchemaList)) {
            NSMutableArray *schemas = [NSMutableArray array];
            for (size_t i = 0; i < rimeSchemaList.size; i++) {
                NSDictionary *schema = @{
                    @"schemaId": [NSString stringWithUTF8String:rimeSchemaList.list[i].schema_id],
                    @"name": [NSString stringWithUTF8String:rimeSchemaList.list[i].name]
                };
                [schemas addObject:schema];
            }
            schemaList = schemas;
            rime->free_schema_list(&rimeSchemaList);
        }
    });
    return schemaList;
}

#pragma mark - 输入控制

- (nullable NSString *)getInput:(RimeSessionId)sessionId {
    __block NSString *input = nil;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        const char *inputStr = rime->get_input(sessionId);
        if (inputStr) {
            input = [NSString stringWithUTF8String:inputStr];
        }
    });
    return input;
}

- (BOOL)setInput:(RimeSessionId)sessionId input:(NSString *)input {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->set_input(sessionId, input.UTF8String);
    });
    return result;
}

- (NSInteger)getCaretPos:(RimeSessionId)sessionId {
    __block NSInteger pos = 0;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        pos = rime->get_caret_pos(sessionId);
    });
    return pos;
}

- (void)setCaretPos:(RimeSessionId)sessionId caretPos:(NSInteger)caretPos {
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        rime->set_caret_pos(sessionId, (int)caretPos);
    });
}

#pragma mark - 工具方法

- (NSString *)getVersion {
    return [NSString stringWithUTF8String:rime->get_version()];
}

- (BOOL)startMaintenance:(BOOL)fullCheck {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->start_maintenance(fullCheck);
        
        // 如果维护任务失败，尝试强制重新部署
        if (!result) {
            NSLog(@"[RimeEngine] 维护任务失败，尝试强制重新部署工作空间");
            result = rime->deploy();
            if (result) {
                NSLog(@"[RimeEngine] ✅ 强制工作空间部署成功");
            } else {
                NSLog(@"[RimeEngine] ❌ 强制工作空间部署失败");
            }
        }
    });
    return result;
}

- (BOOL)isMaintenancing {
    __block BOOL result = NO;
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        result = rime->is_maintenance_mode();
    });
    return result;
}

- (void)joinMaintenanceThread {
    dispatch_sync(self.engineQueue, ^{
        [self checkInitialized];
        rime->join_maintenance_thread();
    });
}

#pragma mark - Private Methods

- (void)checkInitialized {
    if (!self.isInitialized) {
        [NSException raise:NSInternalInconsistencyException 
                    format:@"RimeEngine is not initialized. Call initializeWithTraits: first."];
    }
}

- (BOOL)prepareSharedDataDirectory:(NSString *)sharedDataDir {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;
    
    // 创建共享数据目录
    if (![fileManager createDirectoryAtPath:sharedDataDir 
                withIntermediateDirectories:YES 
                                 attributes:nil 
                                      error:&error]) {
        NSLog(@"[RimeEngine] 创建共享数据目录失败: %@", error);
        return NO;
    }
    
    // 创建build目录
    NSString *buildDir = [sharedDataDir stringByAppendingPathComponent:@"build"];
    if (![fileManager createDirectoryAtPath:buildDir
                withIntermediateDirectories:YES
                                 attributes:nil
                                      error:&error]) {
        NSLog(@"[RimeEngine] 创建build目录失败: %@", error);
        return NO;
    }
    
    // 获取资源Bundle
    NSBundle *resourceBundle = [self findResourceBundle];
    if (!resourceBundle) {
        NSLog(@"[RimeEngine] ⚠️ 无法找到资源Bundle，尝试从其他位置加载资源");
        return [self loadResourcesFromAlternativeLocations:sharedDataDir buildDir:buildDir];
    }
    
    NSLog(@"[RimeEngine] ✅ 找到资源Bundle: %@", resourceBundle.bundlePath);
    
    // 从资源Bundle复制预编译文件
    NSString *prebuiltPath = [resourceBundle pathForResource:@"prebuilt" ofType:nil];
    if (prebuiltPath && [fileManager fileExistsAtPath:prebuiltPath]) {
        NSArray *prebuiltFiles = [fileManager contentsOfDirectoryAtPath:prebuiltPath error:&error];
        if (!error) {
            for (NSString *fileName in prebuiltFiles) {
                NSString *srcPath = [prebuiltPath stringByAppendingPathComponent:fileName];
                NSString *dstPath = [buildDir stringByAppendingPathComponent:fileName];
                
                if (![fileManager fileExistsAtPath:dstPath]) {
                    if ([fileManager copyItemAtPath:srcPath toPath:dstPath error:&error]) {
                        NSLog(@"[RimeEngine] ✅ 复制预编译文件: %@", fileName);
                    } else {
                        NSLog(@"[RimeEngine] ❌ 复制预编译文件失败 %@: %@", fileName, error);
                    }
                }
            }
        }
    }
    
    // 从资源Bundle复制整个rime-data目录
    NSString *rimeDataPath = [resourceBundle pathForResource:@"rime-data" ofType:nil];
    
    if (rimeDataPath && [fileManager fileExistsAtPath:rimeDataPath]) {
        // 复制整个rime-data目录的内容到sharedDataDir
        NSArray *rimeDataContents = [fileManager contentsOfDirectoryAtPath:rimeDataPath error:&error];
        if (!error) {
            for (NSString *item in rimeDataContents) {
                NSString *srcPath = [rimeDataPath stringByAppendingPathComponent:item];
                NSString *dstPath = [sharedDataDir stringByAppendingPathComponent:item];
                
                BOOL isDirectory = NO;
                [fileManager fileExistsAtPath:srcPath isDirectory:&isDirectory];
                
                if (![fileManager fileExistsAtPath:dstPath]) {
                    if ([fileManager copyItemAtPath:srcPath toPath:dstPath error:&error]) {
                        NSLog(@"[RimeEngine] ✅ 复制rime-data项目: %@ (目录: %@)", item, isDirectory ? @"是" : @"否");
                        
                        // 如果是目录，验证子目录内容
                        if (isDirectory) {
                            [self verifyDirectoryContents:dstPath withName:item];
                        }
                    } else {
                        NSLog(@"[RimeEngine] ❌ 复制rime-data项目失败 %@: %@", item, error);
                    }
                }
            }
        } else {
            NSLog(@"[RimeEngine] ❌ 读取rime-data目录失败: %@", error);
        }
    }
    
    return YES;
}

// 新增：查找资源Bundle的方法
- (NSBundle *)findResourceBundle {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    
    // 1. 尝试直接从主Bundle查找（最常见的情况）
    NSBundle *mainBundle = [NSBundle mainBundle];
    NSString *bundlePath = [mainBundle pathForResource:@"RimeKitResources" ofType:@"bundle"];
    if (bundlePath && [fileManager fileExistsAtPath:bundlePath]) {
        NSLog(@"[RimeEngine] 在主Bundle中找到资源: %@", bundlePath);
        return [NSBundle bundleWithPath:bundlePath];
    }
    
    // 2. 尝试从framework bundle中查找
    NSBundle *frameworkBundle = [NSBundle bundleForClass:[self class]];
    NSURL *resourceURL = [frameworkBundle URLForResource:@"RimeKitResources" withExtension:@"bundle"];
    
    if (resourceURL) {
        NSBundle *resourceBundle = [NSBundle bundleWithURL:resourceURL];
        if (resourceBundle) {
            NSLog(@"[RimeEngine] 在Framework中找到资源: %@", resourceURL.path);
            return resourceBundle;
        }
    }
    
    // 3. 尝试从主Bundle的Frameworks目录查找
    NSString *frameworksPath = [mainBundle.bundlePath stringByAppendingPathComponent:@"Frameworks"];
    NSArray *frameworks = [fileManager contentsOfDirectoryAtPath:frameworksPath error:nil];
    
    for (NSString *framework in frameworks) {
        if ([framework hasPrefix:@"RimeKit"]) {
            NSString *rimeKitPath = [frameworksPath stringByAppendingPathComponent:framework];
            NSString *resourcesPath = [rimeKitPath stringByAppendingPathComponent:@"Resources"];
            NSString *bundleInFramework = [resourcesPath stringByAppendingPathComponent:@"RimeKitResources.bundle"];
            
            if ([fileManager fileExistsAtPath:bundleInFramework]) {
                NSLog(@"[RimeEngine] 在Frameworks目录中找到资源: %@", bundleInFramework);
                return [NSBundle bundleWithPath:bundleInFramework];
            }
        }
    }
    
    // 4. 列出主Bundle内容帮助调试
    NSLog(@"[RimeEngine] 主Bundle路径: %@", mainBundle.bundlePath);
    NSArray *mainBundleContents = [fileManager contentsOfDirectoryAtPath:mainBundle.bundlePath error:nil];
    NSLog(@"[RimeEngine] 主Bundle内容: %@", mainBundleContents);
    
    return nil;
}

// 新增：从替代位置加载资源（保留原有的兼容性逻辑）
- (BOOL)loadResourcesFromAlternativeLocations:(NSString *)sharedDataDir buildDir:(NSString *)buildDir {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;
    
    // 保留原有的多种查找路径逻辑，用于兼容性
    NSBundle *bundle = [NSBundle bundleForClass:[self class]];
    NSString *frameworkResourcesPath = nil;
    NSString *prebuiltPath = nil;
    
    // 方案1: 尝试从 xcframework 的 Resources 目录加载
    NSString *frameworkPath = [bundle.bundlePath stringByDeletingLastPathComponent];
    NSString *xcframeworkPath = [frameworkPath stringByDeletingLastPathComponent];
    if ([xcframeworkPath hasSuffix:@".xcframework"]) {
        frameworkResourcesPath = [frameworkPath stringByAppendingPathComponent:@"Resources"];
        prebuiltPath = [frameworkResourcesPath stringByAppendingPathComponent:@"prebuilt"];
    }
    
    // 方案2: 直接从主 bundle 加载（用于开发测试）
    if (!prebuiltPath || ![[NSFileManager defaultManager] fileExistsAtPath:prebuiltPath]) {
        NSBundle *mainBundle = [NSBundle mainBundle];
        prebuiltPath = [mainBundle pathForResource:@"prebuilt" ofType:nil];
        if (prebuiltPath) {
            frameworkResourcesPath = [prebuiltPath stringByDeletingLastPathComponent];
        }
    }
    
    if (prebuiltPath && [fileManager fileExistsAtPath:prebuiltPath]) {
        // 复制预编译文件
        NSArray *prebuiltFiles = [fileManager contentsOfDirectoryAtPath:prebuiltPath error:&error];
        if (!error) {
            for (NSString *fileName in prebuiltFiles) {
                NSString *srcPath = [prebuiltPath stringByAppendingPathComponent:fileName];
                NSString *dstPath = [buildDir stringByAppendingPathComponent:fileName];
                
                if (![fileManager fileExistsAtPath:dstPath]) {
                    [fileManager copyItemAtPath:srcPath toPath:dstPath error:&error];
                }
            }
        }
    }
    
    // 复制rime-data目录
    NSString *rimeDataPath = nil;
    if (frameworkResourcesPath) {
        rimeDataPath = [frameworkResourcesPath stringByAppendingPathComponent:@"rime-data"];
    }
    
    if (rimeDataPath && [fileManager fileExistsAtPath:rimeDataPath]) {
        // 复制整个rime-data目录的内容到sharedDataDir
        NSArray *rimeDataContents = [fileManager contentsOfDirectoryAtPath:rimeDataPath error:&error];
        if (!error) {
            for (NSString *item in rimeDataContents) {
                NSString *srcPath = [rimeDataPath stringByAppendingPathComponent:item];
                NSString *dstPath = [sharedDataDir stringByAppendingPathComponent:item];
                
                BOOL isDirectory = NO;
                [fileManager fileExistsAtPath:srcPath isDirectory:&isDirectory];
                
                if (![fileManager fileExistsAtPath:dstPath]) {
                    if ([fileManager copyItemAtPath:srcPath toPath:dstPath error:&error]) {
                        NSLog(@"[RimeEngine] ✅ 复制rime-data项目: %@ (目录: %@)", item, isDirectory ? @"是" : @"否");
                        
                        // 如果是目录，验证子目录内容
                        if (isDirectory) {
                            [self verifyDirectoryContents:dstPath withName:item];
                        }
                    } else {
                        NSLog(@"[RimeEngine] ❌ 复制rime-data项目失败 %@: %@", item, error);
                    }
                }
            }
        } else {
            NSLog(@"[RimeEngine] ❌ 读取rime-data目录失败: %@", error);
        }
    }
    
    return YES;
}

// 从资源Bundle复制资源文件
- (void)copyResourcesFromBundle:(NSBundle *)resourceBundle toSharedDataDir:(NSString *)sharedDataDir buildDir:(NSString *)buildDir {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;
    
    // 确保build目录存在
    if (![fileManager fileExistsAtPath:buildDir]) {
        [fileManager createDirectoryAtPath:buildDir withIntermediateDirectories:YES attributes:nil error:&error];
        if (error) {
            NSLog(@"[RimeEngine] ❌ 创建build目录失败: %@", error);
        }
    }
    
    // 从资源Bundle复制预编译文件
    NSString *prebuiltPath = [resourceBundle pathForResource:@"prebuilt" ofType:nil];
    if (prebuiltPath && [fileManager fileExistsAtPath:prebuiltPath]) {
        NSArray *prebuiltFiles = [fileManager contentsOfDirectoryAtPath:prebuiltPath error:&error];
        if (!error) {
            for (NSString *fileName in prebuiltFiles) {
                NSString *srcPath = [prebuiltPath stringByAppendingPathComponent:fileName];
                NSString *dstPath = [buildDir stringByAppendingPathComponent:fileName];
                
                if (![fileManager fileExistsAtPath:dstPath]) {
                    if ([fileManager copyItemAtPath:srcPath toPath:dstPath error:&error]) {
                        NSLog(@"[RimeEngine] ✅ 复制预编译文件: %@", fileName);
                    } else {
                        NSLog(@"[RimeEngine] ❌ 复制预编译文件失败 %@: %@", fileName, error);
                    }
                }
            }
        }
    }
    
    // 从资源Bundle复制整个rime-data目录
    NSString *rimeDataPath = [resourceBundle pathForResource:@"rime-data" ofType:nil];
    
    if (rimeDataPath && [fileManager fileExistsAtPath:rimeDataPath]) {
        // 复制整个rime-data目录的内容到sharedDataDir
        NSArray *rimeDataContents = [fileManager contentsOfDirectoryAtPath:rimeDataPath error:&error];
        if (!error) {
            for (NSString *item in rimeDataContents) {
                NSString *srcPath = [rimeDataPath stringByAppendingPathComponent:item];
                NSString *dstPath = [sharedDataDir stringByAppendingPathComponent:item];
                
                BOOL isDirectory = NO;
                [fileManager fileExistsAtPath:srcPath isDirectory:&isDirectory];
                
                if (![fileManager fileExistsAtPath:dstPath]) {
                    if ([fileManager copyItemAtPath:srcPath toPath:dstPath error:&error]) {
                        NSLog(@"[RimeEngine] ✅ 复制rime-data项目: %@ (目录: %@)", item, isDirectory ? @"是" : @"否");
                        
                        // 如果是目录，验证子目录内容
                        if (isDirectory) {
                            [self verifyDirectoryContents:dstPath withName:item];
                        }
                    } else {
                        NSLog(@"[RimeEngine] ❌ 复制rime-data项目失败 %@: %@", item, error);
                    }
                }
            }
        } else {
            NSLog(@"[RimeEngine] ❌ 读取rime-data目录失败: %@", error);
        }
    }
}

// 验证关键配置文件是否存在
- (void)verifyConfigurationFiles:(NSString *)buildDir sharedDataDir:(NSString *)sharedDataDir {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSArray *requiredFiles = @[
        @"default.yaml",
        @"rime_ice.schema.yaml",
        @"rime_ice.dict.yaml",
        @"t9.schema.yaml"
    ];
    
    NSLog(@"[RimeEngine] 验证配置文件...");
    
    // 检查 shared data 目录中的文件
    for (NSString *fileName in requiredFiles) {
        NSString *sharedFilePath = [sharedDataDir stringByAppendingPathComponent:fileName];
        NSString *buildFilePath = [buildDir stringByAppendingPathComponent:fileName];
        
        BOOL sharedExists = [fileManager fileExistsAtPath:sharedFilePath];
        BOOL buildExists = [fileManager fileExistsAtPath:buildFilePath];
        
        NSLog(@"[RimeEngine] %@: shared(%@) build(%@)", 
              fileName, 
              sharedExists ? @"✅" : @"❌", 
              buildExists ? @"✅" : @"❌");
        
        // 如果 shared 目录有文件但 build 目录没有，尝试手动复制
        if (sharedExists && !buildExists) {
            NSError *error;
            if ([fileManager copyItemAtPath:sharedFilePath toPath:buildFilePath error:&error]) {
                NSLog(@"[RimeEngine] ✅ 手动复制配置文件到build目录: %@", fileName);
            } else {
                NSLog(@"[RimeEngine] ❌ 手动复制配置文件失败 %@: %@", fileName, error);
            }
        }
    }
    
    // 检查预编译文件
    NSArray *prebuiltFiles = @[
        @"rime_ice.prism.bin",
        @"rime_ice.reverse.bin", 
        @"rime_ice.table.bin",
        @"t9.prism.bin"
    ];
    
    for (NSString *fileName in prebuiltFiles) {
        NSString *buildFilePath = [buildDir stringByAppendingPathComponent:fileName];
        BOOL exists = [fileManager fileExistsAtPath:buildFilePath];
        NSLog(@"[RimeEngine] 预编译文件 %@: %@", fileName, exists ? @"✅" : @"❌");
    }
}

- (void)copyDirectoryFrom:(NSString *)srcPath to:(NSString *)dstPath {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;
    
    // 删除目标目录
    if ([fileManager fileExistsAtPath:dstPath]) {
        [fileManager removeItemAtPath:dstPath error:nil];
    }
    
    // 复制目录
    [fileManager copyItemAtPath:srcPath toPath:dstPath error:&error];
    if (error) {
        NSLog(@"[RimeEngine] 复制目录失败: %@", error);
    }
}

// 验证目录内容
- (void)verifyDirectoryContents:(NSString *)directoryPath withName:(NSString *)directoryName {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;
    
    // 获取子目录/文件列表
    NSArray *contents = [fileManager contentsOfDirectoryAtPath:directoryPath error:&error];
    if (error) {
        NSLog(@"[RimeEngine] ❌ 无法读取目录 %@: %@", directoryName, error);
        return;
    }
    
    NSLog(@"[RimeEngine] 📁 目录 %@ 包含 %lu 个项目:", directoryName, (unsigned long)contents.count);
    
    // 特别关注重要的子目录
    NSArray *importantSubdirs = @[@"lua", @"opencc", @"cn_dicts"];
    for (NSString *subdir in importantSubdirs) {
        if ([directoryName isEqualToString:subdir] || [contents containsObject:subdir]) {
            NSString *subdirPath = [directoryPath stringByAppendingPathComponent:subdir];
            BOOL isDir = NO;
            if ([fileManager fileExistsAtPath:subdirPath isDirectory:&isDir] && isDir) {
                NSArray *subdirContents = [fileManager contentsOfDirectoryAtPath:subdirPath error:nil];
                NSLog(@"[RimeEngine]   └── 子目录 %@ 包含 %lu 个文件", subdir, (unsigned long)subdirContents.count);
            }
        }
    }
    
    // 列出前5个文件作为示例
    NSInteger maxShow = MIN(5, contents.count);
    for (NSInteger i = 0; i < maxShow; i++) {
        NSLog(@"[RimeEngine]   - %@", contents[i]);
    }
    if (contents.count > maxShow) {
        NSLog(@"[RimeEngine]   ... 和其他 %lu 个文件", (unsigned long)(contents.count - maxShow));
    }
}

// 验证重要目录是否存在
- (void)verifyImportantDirectories:(NSString *)sharedDataDir {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    
    NSLog(@"[RimeEngine] 验证重要目录...");
    
    // 定义需要验证的重要目录
    NSArray *importantDirs = @[
        @"lua",
        @"opencc", 
        @"cn_dicts"
    ];
    
    for (NSString *dirName in importantDirs) {
        NSString *dirPath = [sharedDataDir stringByAppendingPathComponent:dirName];
        BOOL isDir = NO;
        BOOL exists = [fileManager fileExistsAtPath:dirPath isDirectory:&isDir];
        
        if (exists && isDir) {
            NSError *error;
            NSArray *contents = [fileManager contentsOfDirectoryAtPath:dirPath error:&error];
            if (!error) {
                NSLog(@"[RimeEngine] ✅ 目录 %@ 存在，包含 %lu 个文件", dirName, (unsigned long)contents.count);
                
                // 对于某些目录，列出关键文件
                if ([dirName isEqualToString:@"opencc"]) {
                    for (NSString *file in contents) {
                        if ([file hasSuffix:@".ocd2"] || [file hasSuffix:@".json"]) {
                            NSLog(@"[RimeEngine]   - %@", file);
                        }
                    }
                } else if ([dirName isEqualToString:@"lua"]) {
                    for (NSString *file in contents) {
                        if ([file hasSuffix:@".lua"]) {
                            NSLog(@"[RimeEngine]   - %@", file);
                        }
                    }
                }
            } else {
                NSLog(@"[RimeEngine] ❌ 无法读取目录 %@ 的内容: %@", dirName, error);
            }
        } else {
            NSLog(@"[RimeEngine] ❌ 目录 %@ 不存在或不是目录", dirName);
        }
    }
}

@end