# RimeKit iOS 词库集成完成

## 总结

词库文件已成功集成到 RimeKit.xcframework 中！现在使用 `build_final.sh` 脚本构建时会自动包含所有必要的词库文件。

## 关键更新

### 1. 构建脚本 (`build_final.sh`)
- 在构建完成后自动调用 `add_resources_to_xcframework.sh` 添加资源文件
- 更新了集成文档，添加了词库文件配置说明

### 2. 资源添加脚本 (`add_resources_to_xcframework.sh`)
- 适配了 `build_final.sh` 生成的 xcframework 结构
- 将资源文件添加到每个平台目录下的 Resources 文件夹
- 同时生成独立的 RimeKitResources.bundle 作为备选方案

### 3. RimeEngine 资源加载逻辑
- 实现了多种资源查找策略：
  1. 从 xcframework 的 Resources 目录加载
  2. 从应用 Frameworks 目录查找
  3. 从独立资源包加载
  4. 从主 bundle 加载（开发测试用）
- 确保在各种集成场景下都能正确找到词库文件

## 文件结构

构建后的 xcframework 结构：
```
output/
├── RimeKit.xcframework/
│   ├── Info.plist
│   ├── ios-arm64/
│   │   ├── Headers/
│   │   ├── libRimeKit.a
│   │   └── Resources/
│   │       ├── prebuilt/
│   │       │   ├── rime_ice.prism.bin
│   │       │   ├── rime_ice.reverse.bin
│   │       │   ├── rime_ice.table.bin
│   │       │   └── t9.prism.bin
│   │       └── rime-data/
│   │           ├── default.yaml
│   │           ├── rime_ice.schema.yaml
│   │           ├── symbols_v.yaml
│   │           └── t9.schema.yaml
│   └── ios-arm64_x86_64-simulator/
│       └── [相同结构]
├── RimeKitResources.bundle/  # 独立资源包（可选）
├── Integration.md            # 集成指南
└── TestExample.m            # 测试代码示例
```

## 使用方法

### 构建
```bash
./build_final.sh
```
这会自动构建 xcframework 并添加词库文件。

### 集成到项目
1. 将 `output/RimeKit.xcframework` 添加到 Xcode 项目
2. 添加所有依赖的 xcframework（librime、boost、icu 等）
3. 确保 "Embed" 设置为 "Do Not Embed"（静态库）

### 初始化代码
```objc
RimeInputManager *manager = [RimeInputManager sharedManager];
if ([manager initialize]) {
    NSLog(@"RimeKit 初始化成功，词库文件已加载");
}
```

## 验证测试

运行测试脚本验证集成：
```bash
./test_integration.sh
```

测试结果显示所有词库文件都已正确集成到两个平台架构中。

## 注意事项

1. 词库文件会在首次初始化时自动从 framework 复制到应用沙盒目录
2. 总共约 10MB 的词库文件，确保设备有足够存储空间
3. 使用预编译的二进制词库文件，避免运行时编译，提高启动速度
4. 支持雾凇拼音（rime_ice）和九宫格（t9）两种输入方案

现在你可以在 iOS 应用中使用完整的 Rime 输入法功能了！