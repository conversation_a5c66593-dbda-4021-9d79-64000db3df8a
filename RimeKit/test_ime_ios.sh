#!/bin/bash

# 构建并运行 ime-ios 项目测试脚本

echo "=== ime-ios 项目测试 ==="

# 定义路径
PROJECT_PATH="/Users/<USER>/myfiles/myprojects/android_pj/librime/ime-ios/ime-ios.xcodeproj"
SCHEME="ime-ios"

# 清理旧的构建
echo "🧹 清理旧的构建..."
xcodebuild clean -project "$PROJECT_PATH" -scheme "$SCHEME" -quiet

# 构建项目（模拟器）
echo "🔨 构建项目..."
xcodebuild build \
    -project "$PROJECT_PATH" \
    -scheme "$SCHEME" \
    -sdk iphonesimulator \
    -destination "platform=iOS Simulator,name=iPhone 16,OS=18.1" \
    -quiet \
    -configuration Debug

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo ""
    echo "项目已成功重构为使用新的 RimeManager 极简 API："
    echo "- 仅需 3 行代码初始化"
    echo "- 自动处理资源加载"
    echo "- 简化的输入和候选词管理"
    echo ""
    echo "要运行应用，请在 Xcode 中打开项目并选择模拟器运行。"
else
    echo "❌ 构建失败"
    exit 1
fi