# Rime schema
# encoding: utf-8

schema:
  schema_id: clover
  name: 🍀️四叶草简体拼音
  version: "1.1.0"
  author:
    - fkxxyz - https://www.fkxxyz.com
    - SivanLaai - https://sivanlaai.github.io
    - warm-ice0x00
  description: |
    由🍀️四叶草亲自定制，特点：
      1. 结合搜狗输入法的输入习惯
      2. 默认开启 emoji 表情支持
      3. 加上很多特殊符号的输入支持
      4. 繁简切换支持
      5. 许多智能纠错
    由 SivanLaai 补充：
      1. 支持符号输入
      2. 支持笔画反查
    由 warm-ice0x00 添加定制简体中文语言模型。
  dependencies:
    # - emoji
    # - stroke

switches:
  - name: ascii_mode
    reset: 0
    states: [ 中, 英 ]
  - name: full_shape
    reset: 0
    states: [ 半, 全 ]
  - name: zh_simp_s2t
    reset: 0
    states: [ 简, 繁 ]
  - name: ascii_punct
    reset: 0
    states: [ 。，, ．， ]
  - name: symbol_support
    reset: 0
    states: [ "无符", "符" ]
  - name: emoji_suggestion
    reset: 0
    states: [ 😑️, 😁️ ]

engine:
  processors:
    - ascii_composer
    - recognizer
    - key_binder
    - speller
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - ascii_segmentor
    - matcher
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - punct_translator
    - table_translator@custom_phrase
    - reverse_lookup_translator
    - script_translator
  filters:
    # - simplifier@emoji_suggestion
    - simplifier@symbol_support
    - simplifier
    - uniquifier

simplifier:
  option_name: zh_simp_s2t
  opencc_config: s2t.json

# emoji_suggestion:
#   opencc_config: emoji.json
#   option_name: emoji_suggestion
#   tips: all

symbol_support:
  opencc_config: symbol.json
  option_name: symbol_support
  tips: all

speller:
  alphabet: zyxwvutsrqponmlkjihgfedcba
  delimiter: " '"
  algebra:
    ### 模糊音
    # 声母
    - derive/^([zcs])h/$1/          # zh* ch* sh* 派生出 z* c* s*
    - derive/^([zcs])([^h])/$1h$2/  # z* c* s*    派生出 zh* ch* sh*
    - derive/^l/n/  # 解释：为 l 开头的拼写派生出 n 开头，即 nai 也可以输出 lai（来、莱、赖……）
    - derive/^n/l/  #      lai 可输出 nai（奶、乃、奈……）。 可以单向或成对儿启用模糊音
    - derive/^f/h/
    - derive/^h/f/
    - derive/^l/r/
    - derive/^r/l/
    - derive/^g/k/
    - derive/^k/g/
    # 韵母
    - derive/ang$/an/
    - derive/an$/ang/
    - derive/eng$/en/
    - derive/en$/eng/
    - derive/in$/ing/
    - derive/ing$/in/
    - derive/ian$/iang/
    - derive/iang$/ian/
    - derive/uan$/uang/
    - derive/uang$/uan/
    - derive/ai$/an/
    - derive/an$/ai/
    - derive/ong$/un/
    - derive/un$/ong/
    - derive/ong$/on/
    - derive/iong$/un/
    - derive/un$/iong/
    - derive/ong$/eng/
    - derive/eng$/ong/
    # 拼音音节
    - derive/^fei$/hui/
    - derive/^hui$/fei/
    - derive/^hu$/fu/
    - derive/^fu$/hu/
    - derive/^wang$/huang/
    - derive/^huang$/wang/
    # 基础
    - abbrev/^([a-z]).+$/$1/
    - abbrev/^([zcs]h).+$/$1/

    # 补全
    - derive/([dtngkhrzcs])o(u|ng)$/$1o/   # o = ou; o = ong
    - derive/ong$/on/      # on = ong
    - derive/^ding$/din/     # din = ding

    # 处理 v 和 u
    - derive/^([nl])ue$/$1ve/   # nve = nue; lve = lue
    - derive/^([jqxy])u/$1v/    # v = u; v = u

    # 智能纠错
    - derive/ao$/oa/       # oa = ao
    - derive/([iu])a(o|ng?)$/a$1$2/   # aio = iao; aing = iang; aung = uang
    - derive/([aeiou])ng$/$1gn/   # gn = ng
    - derive/un$/uen/    # uen = un
    - derive/ui$/uei/    # uei = ui
    - derive/iu$/iou/    # iou = iu
    - derive/tie$/tei/    # tei = tie
    - derive/i$/ii/      # ii = i  # i 不小心按两下
    - derive/u$/uu/      # uu = u  # u 不小心按两下

    # 自动纠错
    - derive/^([nl])ve$/$1ue/
    - derive/^([jqxy])u/$1v/
    - derive/^([nl])ue$/$1ve/
    - derive/^([jqxy])v/$1u/
    - derive/([zcs])h(a|e|i|u|ai|ei|an|en|ou|uo|ua|un|ui|uan|uai|uang|ang|eng|ong)$/h$1$2/  # hzi → zhi
    - derive/([zcs])h([aeiu])$/$1$2h/  # zih → zhi
    - derive/^([wghk])ai$/$1ia/  # wia → wai
    - derive/([wfghkz])ei$/$1ie/  # wie → wei
    - derive/([jqx])ie$/$1ei/  # jei → jie
    - derive/([rtypsdghklzcbnm])ao$/$1oa/
    - derive/([ypfm])ou$/$1uo/
    - derive/([wrtypsdfghklzcbnm])ang$/$1nag/
    - derive/([wrtypsdfghklzcbnm])ang$/$1agn/
    - derive/([wrtpsdfghklzcbnm])eng$/$1neg/
    - derive/([wrtpsdfghklzcbnm])eng$/$1egn/
    - derive/([qtypdjlxbnm])ing$/$1nig/
    - derive/([qtypdjlxbnm])ing$/$1ign/
    - derive/([rtysdghklzcn])ong$/$1nog/
    - derive/([rtysdghklzcn])ong$/$1ogn/
    - derive/([qtpdjlxbnm])iao$/$1ioa/
    - derive/([qtpdjlxbnm])iao$/$1oia/
    - derive/([rtsghkzc])ui$/$1iu/
    - derive/([qjlxnm])iu$/$1ui/
    - derive/([qjlxn])iang$/$1aing/
    - derive/([qjlxn])iang$/$1inag/
    - derive/([g|k|h|zh|sh])ua$/$1au/
    - derive/([g|h|k|zh|ch|sh])uai$/$1aui/
    - derive/([qrtysdghjklzxcn])uan$/$1aun/
    - derive/([nlyjqx])ue$/$1eu/
    - derive/([g|h|k|zh|ch|sh])uang$/$1aung/
    - derive/([g|h|k|zh|ch|sh])uang$/$1uagn/
    - derive/([g|h|k|zh|ch|sh])uang$/$1unag/
    - derive/([g|h|k|zh|ch|sh])uang$/$1augn/
    - derive/([jqx])iong$/$1inog/
    - derive/([jqx])iong$/$1oing/
    - derive/([jqx])iong$/$1iogn/
    - derive/([jqx])iong$/$1oign/
    - derive/([rtsdghkzc])o(u|ng)$/$1o/ # do → dou|dong
    - derive/(.+)ong$/$1on/ # lon → long
    - derive/([tl])eng$/$1en/ # ten → teng
    - derive/([qwrtypsdfghjklzxcbnm])([aeio])ng$/$1ng/ # lng → lang、leng、ling、long

grammar:
  language: zh-hans-t-huayu-v7-bgw

translator:
  dictionary: clover
  contextual_suggestions: true
  max_homophones: 2
  max_homographs: 2
  preedit_format:
    - xform/([nl])v/$1ü/
    - xform/([nl])ue/$1üe/
    - xform/([jqxy])v/$1u/

custom_phrase:
  dictionary: ""
  user_dict: custom_phrase
  db_class: stabledb
  enable_completion: false
  enable_sentence: false
  initial_quality: 1

reverse_lookup:
  dictionary: stroke
  enable_completion: true
  prefix: "`"
  suffix: "'"
  tips: 〔筆畫〕
  preedit_format:
    - xlit/hspnz/一丨丿丶乙/
  comment_format:
    - xform/([nl])v/$1ü/

punctuator:
  import_preset: symbols

key_binder:
  import_preset: default

recognizer:
  import_preset: default
  patterns:
    punct: '^/([0-9]0?|[A-Za-z]+)$'
    reverse_lookup: "`[a-z]*'?$"

install_files: >-
  emoji_suggestion.yaml
  opencc/*.*