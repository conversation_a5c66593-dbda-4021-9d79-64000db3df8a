# Clover四叶草全系列输入法方案预编译信息
# 此文件记录预编译的基本信息

prebuild:
  project: "clover_all"
  project_name: "Clover四叶草全系列输入法方案"
  timestamp: 2025-07-24T05:52:15Z
  version: "20250724"
  
  # 目录结构
  directory_structure:
    - path: "/prebuilt/"
      description: "预编译词库文件 (*.bin files)"
      type: "dictionaries"
    - path: "/rime-data/"
      description: "Rime配置规则文件 (*.schema.yaml, *.yaml, etc.)"
      type: "configurations"
    - path: "/rime-data/opencc/"
      description: "OpenCC转换配置文件"
      type: "conversion_rules"
  
  # 包含的方案
  schemas:
    - id: clover
      name: Clover四叶草拼音
    - id: clover_jiugong
      name: Clover四叶草九宫拼音
  
  # 生成的词典文件 (prebuilt目录下)
  dictionary_binaries:
    - clover_jiugong.prism.bin
    - clover.prism.bin
    - clover.reverse.bin
    - clover.table.bin
    - luna_pinyin.prism.bin
    - luna_pinyin.reverse.bin
    - luna_pinyin.table.bin
  
  # 配置文件 (rime-data目录下)
  schema_files:
    - clover_jiugong.schema.yaml
    - clover.schema.yaml
    - luna_pinyin.schema.yaml
  
  system_config_files:

    - essay.txt

# 使用说明：
# 1. prebuilt/ 目录包含预编译的词典文件 (*.bin)，运行时直接加载
# 2. rime-data/ 目录包含Rime配置文件：
#    - *.schema.yaml: 输入法方案定义文件
#    - default.yaml, symbols.yaml: 系统配置文件  
#    - essay.txt: 语言模型文件
#    - opencc/: 繁简转换等配置文件
# 3. *.dict.yaml 词典源文件已预编译为*.bin，无需保留
# 4. 包含基础拼音、九宫格输入、地球拼音（带声调）三种模式
# 5. 如果源词典有更新，需要重新预编译
