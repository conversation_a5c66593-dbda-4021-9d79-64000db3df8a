# RimeKit XCFramework 集成指南

## 概述
RimeKit.xcframework 是一个静态库 XCFramework，支持 iOS 设备。

## 支持的架构
- iOS 设备: arm64

## 集成步骤

### 1. 添加 XCFramework
1. 将 \`RimeKit.xcframework\` 拖入你的 Xcode 项目
2. 在弹出的对话框中选择 "Copy items if needed"
3. 确保选中你的 target

### 2. 配置链接
1. 选择你的项目 target
2. 在 "General" 标签页找到 "Frameworks, Libraries, and Embedded Content"
3. 确保 RimeKit.xcframework 的 "Embed" 设置为 "Do Not Embed"

### 3. 添加依赖库
确保项目中包含以下依赖的 XCFramework：
- librime.xcframework
- boost_*.xcframework (atomic, filesystem, locale, regex, system)
- icu*.xcframework (data, i18n, io, uc)
- libglog.xcframework
- libleveldb.xcframework
- libmarisa.xcframework
- libopencc.xcframework
- libyaml-cpp.xcframework

### 4. 词库文件配置
RimeKit 需要词库文件才能正常工作。请按以下步骤配置：

1. 将 RimeKitResources.bundle 添加到项目中
2. 确保在 "Build Phases" -> "Copy Bundle Resources" 中包含此 bundle
3. 资源 bundle 包含：
   - prebuilt/: 预编译的词库文件
   - rime-data/: Rime 配置文件

### 5. 使用示例

```objc
#import <RimeKit/RimeKit.h>

@implementation YourViewController

- (void)setupRime {
    // 1. 获取文档目录
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDir = [paths firstObject];
    
    // 2. 创建配置
    RimeTraits *traits = [RimeTraits defaultTraitsWithBundleIdentifier:@"com.example.app"
                                                         documentsDir:documentsDir
                                                              version:@"1.0.0"];
    
    // 3. 初始化引擎
    RimeEngine *engine = [RimeEngine sharedEngine];
    if ([engine initializeWithTraits:traits]) {
        NSLog(@"RimeKit 初始化成功");
        
        // 4. 创建输入会话
        RimeInputManager *manager = [RimeInputManager sharedManager];
        RimeInputSession *session = [manager createSession];
        session.delegate = self;
        
        // 5. 选择输入方案（重要！）
        [session selectSchema:@"rime_ice"];  // 或 @"t9"
        
        // 6. 开始输入
        [session processCharacter:'n'];
        [session processCharacter:'i'];
        [session processCharacter:'h'];
        [session processCharacter:'a'];
        [session processCharacter:'o'];
    }
}

// RimeInputSessionDelegate 方法
- (void)onCandidatesUpdate:(RimeCandidateList *)candidates {
    // 处理候选词更新
    for (int i = 0; i < candidates.count; i++) {
        RimeCandidate *candidate = [candidates candidateAtIndex:i];
        NSLog(@"候选词 %d: %@", i, candidate.text);
    }
}

- (void)onCompositionUpdate:(RimeComposition *)composition {
    // 处理组合更新
    NSLog(@"当前输入: %@", composition.preedit);
}

- (void)onTextCommit:(NSString *)text {
    // 处理文本提交
    NSLog(@"提交文本: %@", text);
}

@end
```

## 注意事项

1. **必须选择输入方案**：在创建会话后，必须调用 `selectSchema` 选择输入方案
2. **必须添加资源 Bundle**：RimeKitResources.bundle 包含必要的词库和配置文件
3. 这是一个静态库，不要设置为 "Embed & Sign"
4. 确保所有依赖库都已添加到项目中
5. 初始化时会自动创建必要的数据目录
6. 资源文件会自动从 Bundle 复制到应用沙盒目录

## 故障排除

### 无法获取候选词
- 确保已调用 `selectSchema` 选择输入方案
- 检查资源文件是否正确加载：使用 `[RimeKitResourceLoader checkResourcesAvailable]`

### 链接错误
如果遇到 "undefined symbols" 错误，请检查：
1. 所有依赖的 XCFramework 是否已添加
2. Build Settings 中的 "Other Linker Flags" 是否包含 "-ObjC"
3. 架构设置是否正确

### 运行时错误
如果遇到运行时错误：
1. 检查控制台日志获取详细错误信息
2. 确保有写入权限到用户数据目录
3. 验证资源 Bundle 是否存在并可访问

## 版本信息
- RimeKit 版本: 1.0.0
- 最低 iOS 版本: 12.0
- 构建时间: $(date)
