#import <Foundation/Foundation.h>

typedef uintptr_t RimeSessionId;

@class RimeTraits;

NS_ASSUME_NONNULL_BEGIN

/**
 * iOS Objective-C封装librime输入法引擎的核心功能
 * 提供会话管理、按键处理、候选词获取、文本提交等功能
 */
@interface RimeEngine : NSObject

// 单例模式
+ (instancetype)sharedEngine;

// ================ 核心生命周期管理 ================

/**
 * 初始化Rime引擎
 * @param traits 引擎配置参数
 * @return 初始化是否成功
 */
- (BOOL)initializeWithTraits:(RimeTraits *)traits;

/**
 * 关闭Rime引擎，释放所有资源
 */
- (void)finalize;

/**
 * 检查引擎是否已初始化
 */
@property (nonatomic, readonly) BOOL isInitialized;

// ================ 会话管理 ================

/**
 * 创建新的输入会话
 * @return 会话ID，0表示失败
 */
- (RimeSessionId)createSession;

/**
 * 查找会话是否存在
 * @param sessionId 会话ID
 * @return 是否存在
 */
- (BOOL)findSession:(RimeSessionId)sessionId;

/**
 * 销毁输入会话
 * @param sessionId 会话ID
 * @return 是否成功
 */
- (BOOL)destroySession:(RimeSessionId)sessionId;

// ================ 按键处理 ================

/**
 * 处理按键事件
 * @param sessionId 会话ID
 * @param keyCode 按键码
 * @param mask 修饰键掩码
 * @return 按键是否被处理
 */
- (BOOL)processKey:(RimeSessionId)sessionId keyCode:(int)keyCode mask:(int)mask;

/**
 * 提交当前组合
 * @param sessionId 会话ID
 * @return 是否成功
 */
- (BOOL)commitComposition:(RimeSessionId)sessionId;

/**
 * 清除当前组合
 * @param sessionId 会话ID
 */
- (void)clearComposition:(RimeSessionId)sessionId;

// ================ 文本获取 ================

/**
 * 获取待提交文本
 * @param sessionId 会话ID
 * @return 提交文本，无则返回nil
 */
- (nullable NSString *)getCommitText:(RimeSessionId)sessionId;

/**
 * 获取输入上下文
 * @param sessionId 会话ID
 * @return 上下文信息字典，无则返回nil
 */
- (nullable NSDictionary *)getContext:(RimeSessionId)sessionId;

/**
 * 获取输入状态
 * @param sessionId 会话ID
 * @return 状态信息字典，无则返回nil
 */
- (nullable NSDictionary *)getStatus:(RimeSessionId)sessionId;

// ================ 候选词操作 ================

/**
 * 选择候选词
 * @param sessionId 会话ID
 * @param index 候选词索引
 * @return 是否成功
 */
- (BOOL)selectCandidate:(RimeSessionId)sessionId index:(int)index;

/**
 * 选择当前页的候选词
 * @param sessionId 会话ID
 * @param index 当前页内的索引
 * @return 是否成功
 */
- (BOOL)selectCandidateOnCurrentPage:(RimeSessionId)sessionId index:(int)index;

/**
 * 删除候选词
 * @param sessionId 会话ID
 * @param index 候选词索引
 * @return 是否成功
 */
- (BOOL)deleteCandidate:(RimeSessionId)sessionId index:(int)index;

/**
 * 翻页
 * @param sessionId 会话ID
 * @param backward YES为向前翻页，NO为向后翻页
 * @return 是否成功
 */
- (BOOL)changePage:(RimeSessionId)sessionId backward:(BOOL)backward;

// ================ 配置和选项 ================

/**
 * 设置选项
 * @param sessionId 会话ID
 * @param option 选项名称
 * @param value 选项值
 */
- (void)setOption:(RimeSessionId)sessionId option:(NSString *)option value:(BOOL)value;

/**
 * 获取选项
 * @param sessionId 会话ID
 * @param option 选项名称
 * @return 选项值
 */
- (BOOL)getOption:(RimeSessionId)sessionId option:(NSString *)option;

/**
 * 选择输入方案
 * @param sessionId 会话ID
 * @param schemaId 方案ID
 * @return 是否成功
 */
- (BOOL)selectSchema:(RimeSessionId)sessionId schemaId:(NSString *)schemaId;

/**
 * 获取当前输入方案
 * @param sessionId 会话ID
 * @return 方案ID，无则返回nil
 */
- (nullable NSString *)getCurrentSchema:(RimeSessionId)sessionId;

/**
 * 获取输入方案列表
 * @return 方案列表数组，无则返回nil
 */
- (nullable NSArray *)getSchemaList;

// ================ 输入控制 ================

/**
 * 获取当前输入串
 * @param sessionId 会话ID
 * @return 输入串，无则返回nil
 */
- (nullable NSString *)getInput:(RimeSessionId)sessionId;

/**
 * 设置输入串
 * @param sessionId 会话ID
 * @param input 输入串
 * @return 是否成功
 */
- (BOOL)setInput:(RimeSessionId)sessionId input:(NSString *)input;

/**
 * 获取光标位置
 * @param sessionId 会话ID
 * @return 光标位置
 */
- (NSInteger)getCaretPos:(RimeSessionId)sessionId;

/**
 * 设置光标位置
 * @param sessionId 会话ID
 * @param caretPos 光标位置
 */
- (void)setCaretPos:(RimeSessionId)sessionId caretPos:(NSInteger)caretPos;

// ================ 工具方法 ================

/**
 * 获取Rime版本号
 * @return 版本字符串
 */
- (NSString *)getVersion;

/**
 * 开始维护任务
 * @param fullCheck 是否完整检查
 * @return 是否成功
 */
- (BOOL)startMaintenance:(BOOL)fullCheck;

/**
 * 是否正在执行维护任务
 * @return 是否正在维护
 */
- (BOOL)isMaintenancing;

/**
 * 等待维护任务完成
 */
- (void)joinMaintenanceThread;

@end

NS_ASSUME_NONNULL_END