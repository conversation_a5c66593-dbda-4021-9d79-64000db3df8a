//
//  RimeKit.h
//  RimeKit
//
//  Created by chenqg on 2025/7/10.
//

#import <Foundation/Foundation.h>

//! Project version number for RimeKit.
FOUNDATION_EXPORT double RimeKitVersionNumber;

//! Project version string for RimeKit.
FOUNDATION_EXPORT const unsigned char RimeKitVersionString[];

// 导入所有公开的头文件
#import "RimeManager.h"
#import "RimeEngine.h"
#import "RimeTraits.h"
#import "RimeContext.h"
#import "RimeCommit.h"
#import "RimeStatus.h"
#import "RimeSchemaList.h"
#import "RimeComposition.h"
#import "RimeMenu.h"
#import "RimeCandidate.h"
#import "RimeKeyboardEvent.h"
#import "RimeCandidateList.h"
#import "RimeInputSession.h"
#import "RimeInputManager.h"
#import "RimeInputMode.h"
#import "RimeKitResourceLoader.h"

/**
 RimeKit 使用指南
 
 极简版使用方式（推荐）:
 
 1. 初始化:
    RimeManager *manager = [RimeManager sharedManager];
    [manager initializeEngine];
 
 2. 设置回调:
    [manager setImeCallback:self];
 
 3. 处理输入:
    // 输入字符
    RimeKeyboardEvent *event = [RimeKeyboardEvent fromCharacter:'n'];
    RimeCandidateList *candidates = [manager inputKey:event];
    
    // 翻页
    candidates = [manager pageUp];
    candidates = [manager pageDown];
 
 4. 切换输入模式:
    [manager switchInputMode:RimeInputModePinyin];  // 切换到拼音
    [manager switchInputMode:RimeInputModeT9];      // 切换到九键
    
 5. 重置会话:
    [manager reset];
 
 
 高级使用方式（如需更多控制）:
 
 1. 初始化引擎:
    RimeTraits *traits = [RimeTraits defaultTraitsWithBundleIdentifier:@"com.example.app"
                                                         documentsDir:documentsDir
                                                              version:@"1.0.0"];
    RimeEngine *engine = [RimeEngine sharedEngine];
    [engine initializeWithTraits:traits];
 
 2. 创建输入会话:
    RimeInputManager *manager = [RimeInputManager sharedManager];
    RimeInputSession *session = [manager createSession];
 
 3. 选择输入方案:
    [session selectSchema:@"rime_ice"];  // 拼音输入
    // 或者: [session selectSchema:@"t9"];  // T9数字键盘输入
 
 4. 处理输入:
    [session processCharacter:'n'];
    [session processCharacter:'i'];
    RimeCandidateList *candidates = [session candidates];
 
 5. 资源管理:
    使用 RimeKitResourceLoader 来检查和访问资源文件
 */
