#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * Rime输入状态
 * 对应librime的RimeStatus结构体
 */
@interface RimeStatus : NSObject

@property (nonatomic, copy, readonly) NSString *schemaId;
@property (nonatomic, copy, readonly) NSString *schemaName;
@property (nonatomic, readonly) BOOL isDisabled;
@property (nonatomic, readonly) BOOL isComposing;
@property (nonatomic, readonly) BOOL isAsciiMode;
@property (nonatomic, readonly) BOOL isFullShape;
@property (nonatomic, readonly) BOOL isSimplified;
@property (nonatomic, readonly) BOOL isTraditional;
@property (nonatomic, readonly) BOOL isAsciiPunct;

- (instancetype)initWithSchemaId:(NSString *)schemaId
                      schemaName:(NSString *)schemaName
                      isDisabled:(BOOL)isDisabled
                     isComposing:(BOOL)isComposing
                     isAsciiMode:(BOOL)isAsciiMode
                     isFullShape:(BOOL)isFullShape
                    isSimplified:(BOOL)isSimplified
                   isTraditional:(BOOL)isTraditional
                    isAsciiPunct:(BOOL)isAsciiPunct;

@end

NS_ASSUME_NONNULL_END