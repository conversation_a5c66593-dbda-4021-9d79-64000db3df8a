#import <Foundation/Foundation.h>

@class RimeComposition;
@class RimeMenu;

NS_ASSUME_NONNULL_BEGIN

/**
 * Rime输入上下文
 * 对应librime的RimeContext结构体
 */
@interface RimeContext : NSObject

@property (nonatomic, strong, readonly, nullable) RimeComposition *composition;
@property (nonatomic, strong, readonly, nullable) RimeMenu *menu;
@property (nonatomic, copy, readonly, nullable) NSString *commitTextPreview;
@property (nonatomic, copy, readonly) NSArray<NSString *> *selectLabels;

- (instancetype)initWithComposition:(nullable RimeComposition *)composition
                               menu:(nullable RimeMenu *)menu
                  commitTextPreview:(nullable NSString *)commitTextPreview
                       selectLabels:(NSArray<NSString *> *)selectLabels;

@end

NS_ASSUME_NONNULL_END