#!/bin/bash

# iOS Rime词典预编译脚本
# 参考Android项目预编译流程，为iOS生成预编译词典文件
# 用于减少iOS应用初始化时间

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RIMEKIT_DIR="$SCRIPT_DIR"
ASSETS_DIR="$RIMEKIT_DIR/assets"
BUILD_DIR="$RIMEKIT_DIR/build/prebuild"
RIME_DATA_DIR="$ASSETS_DIR/rime-data"
PREBUILT_DIR="$ASSETS_DIR/prebuilt"

echo "🚀 开始预编译iOS Rime词典..."
echo "📂 源目录: $RIME_DATA_DIR"
echo "📂 构建目录: $BUILD_DIR"
echo "📂 输出目录: $PREBUILT_DIR"

# 检查源文件是否存在
if [ ! -d "$RIME_DATA_DIR" ]; then
    echo "❌ 源目录不存在: $RIME_DATA_DIR"
    echo "💡 请确保rime-data目录存在且包含配置文件"
    exit 1
fi

# 创建必要的目录
mkdir -p "$BUILD_DIR"
mkdir -p "$PREBUILT_DIR"

# 清理旧的预编译文件
echo "🧹 清理旧的预编译文件..."
rm -f "$PREBUILT_DIR"/*.bin
rm -rf "$BUILD_DIR"/*

# 复制源文件到构建目录
echo "📋 复制配置文件..."
cp -r "$RIME_DATA_DIR"/* "$BUILD_DIR/"

# 检查是否有rime_deployer工具
RIME_DEPLOYER=""
POSSIBLE_PATHS=(
    "rime_deployer"
    "/usr/local/bin/rime_deployer"
    "/opt/homebrew/bin/rime_deployer"
    "$SCRIPT_DIR/../tools/rime_deployer"
    "$SCRIPT_DIR/tools/rime_deployer"
)

for path in "${POSSIBLE_PATHS[@]}"; do
    if command -v "$path" &> /dev/null || [ -f "$path" ]; then
        RIME_DEPLOYER="$path"
        break
    fi
done

if [ -z "$RIME_DEPLOYER" ]; then
    echo "❌ 未找到rime_deployer工具"
    echo "💡 请安装librime或设置rime_deployer路径"
    echo "   - macOS: brew install librime"
    echo "   - 或手动编译librime并设置PATH"
    exit 1
fi

echo "🔧 使用工具: $RIME_DEPLOYER"

# 进入构建目录
cd "$BUILD_DIR"

# 设置环境变量
export RIME_USER_DIR="$BUILD_DIR"
export RIME_SHARED_DATA_DIR="$BUILD_DIR"

echo "⚙️  正在预编译词典..."

# 尝试预编译
PREBUILD_SUCCESS=false

# 方法1: 使用--prebuild参数
echo "🔄 尝试方法1: 使用--prebuild参数"
if $RIME_DEPLOYER --build "$BUILD_DIR" --prebuild 2>/dev/null; then
    echo "✅ 方法1成功"
    PREBUILD_SUCCESS=true
else
    echo "⚠️  方法1失败，尝试方法2"
    
    # 方法2: 逐个编译方案
    echo "🔄 尝试方法2: 逐个编译方案"
    SCHEMAS=("rime_ice" "t9")
    
    for schema in "${SCHEMAS[@]}"; do
        if [ -f "${schema}.schema.yaml" ]; then
            echo "  📦 编译方案: ${schema}.schema.yaml"
            if $RIME_DEPLOYER --build "$BUILD_DIR" --schema "$schema" 2>/dev/null; then
                echo "    ✅ ${schema} 编译成功"
                PREBUILD_SUCCESS=true
            else
                echo "    ⚠️  ${schema} 编译失败"
            fi
        fi
    done
fi

# 方法3: 如果上述方法都失败，尝试基本部署
if [ "$PREBUILD_SUCCESS" = false ]; then
    echo "🔄 尝试方法3: 基本部署"
    if $RIME_DEPLOYER --build "$BUILD_DIR" 2>/dev/null; then
        echo "✅ 基本部署成功"
        PREBUILD_SUCCESS=true
    else
        echo "❌ 所有预编译方法都失败"
        echo "💡 可能的解决方案："
        echo "   1. 检查词典文件格式是否正确"
        echo "   2. 确认librime版本兼容性"
        echo "   3. 查看详细错误日志"
        exit 1
    fi
fi

# 收集预编译的二进制文件
echo "📦 收集预编译文件..."
FOUND_FILES=0

# 查找所有二进制文件
find "$BUILD_DIR" -name "*.prism.bin" -o -name "*.table.bin" -o -name "*.reverse.bin" | while read file; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        filesize=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0")
        echo "  ✅ 复制: $filename ($(($filesize / 1024))KB)"
        cp "$file" "$PREBUILT_DIR/"
        FOUND_FILES=$((FOUND_FILES + 1))
    fi
done

# 检查是否找到文件
PREBUILT_COUNT=$(find "$PREBUILT_DIR" -name "*.bin" | wc -l)
if [ "$PREBUILT_COUNT" -eq 0 ]; then
    echo "⚠️  未找到预编译文件，可能需要手动触发编译"
    echo "💡 尝试创建测试配置文件"
    
    # 创建最小配置进行测试
    cat > "$BUILD_DIR/test_build.yaml" << EOF
schema:
  schema_id: test
  name: 测试方案
  version: "1.0"

engine:
  processors:
    - ascii_composer
  segmentors:
    - abc_segmentor
  translators:
    - echo_translator
EOF
    
    # 再次尝试编译
    if $RIME_DEPLOYER --build "$BUILD_DIR" 2>/dev/null; then
        find "$BUILD_DIR" -name "*.bin" -exec cp {} "$PREBUILT_DIR/" \;
        PREBUILT_COUNT=$(find "$PREBUILT_DIR" -name "*.bin" | wc -l)
    fi
fi

# 生成预编译信息文件
echo "📄 生成预编译信息..."
cat > "$PREBUILT_DIR/prebuild_info.yaml" << EOF
# iOS Rime预编译信息
# 此文件记录预编译的基本信息

prebuild:
  timestamp: $(date -u +"%Y-%m-%dT%H:%M:%SZ")
  version: "$(date +%Y%m%d)"
  platform: "iOS"
  target_architectures:
    - arm64
    - arm64-simulator
  dictionaries:
$(find "$PREBUILT_DIR" -name "*.bin" -exec basename {} \; | sort | sed 's/^/    - /')
  
# 使用说明：
# 1. 这些二进制文件是iOS平台的预编译词典
# 2. 可以直接部署到iOS应用Bundle中
# 3. 运行时可直接加载，无需重新编译
# 4. 如果源词典有更新，需要重新预编译

# iOS集成说明：
# 1. 将这些文件复制到RimeKit.xcframework的Resources目录
# 2. 在iOS应用启动时，将这些文件复制到用户数据目录
# 3. Rime引擎会自动检测并使用预编译文件
EOF

# 统计文件大小和数量
echo ""
echo "📊 预编译完成统计："

if [ -d "$RIME_DATA_DIR" ]; then
    ORIGINAL_SIZE=$(du -sh "$RIME_DATA_DIR" 2>/dev/null | cut -f1 || echo "未知")
    echo "   原始配置大小: $ORIGINAL_SIZE"
fi

if [ -d "$PREBUILT_DIR" ]; then
    PREBUILT_SIZE=$(du -sh "$PREBUILT_DIR" 2>/dev/null | cut -f1 || echo "未知")
    echo "   预编译文件大小: $PREBUILT_SIZE"
fi

PREBUILT_COUNT=$(find "$PREBUILT_DIR" -name "*.bin" | wc -l)
echo "   预编译文件数量: $PREBUILT_COUNT"

# 列出生成的文件
if [ "$PREBUILT_COUNT" -gt 0 ]; then
    echo ""
    echo "📋 生成的预编译文件："
    find "$PREBUILT_DIR" -name "*.bin" | while read file; do
        filename=$(basename "$file")
        filesize=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0")
        echo "   📄 $filename ($(($filesize / 1024))KB)"
    done
fi

# 生成iOS集成脚本
echo ""
echo "📱 生成iOS集成脚本..."
cat > "$SCRIPT_DIR/integrate_prebuilt_ios.sh" << 'EOF'
#!/bin/bash

# iOS预编译文件集成脚本
# 将预编译文件集成到RimeKit.xcframework中

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PREBUILT_DIR="$SCRIPT_DIR/assets/prebuilt"
XCFRAMEWORK_DIR="$SCRIPT_DIR/output/RimeKit.xcframework"

echo "🍎 集成预编译文件到iOS框架..."

# 检查必要目录
if [ ! -d "$PREBUILT_DIR" ]; then
    echo "❌ 预编译目录不存在: $PREBUILT_DIR"
    echo "💡 请先运行 ./prebuild_dictionaries_ios.sh"
    exit 1
fi

if [ ! -d "$XCFRAMEWORK_DIR" ]; then
    echo "❌ XCFramework不存在: $XCFRAMEWORK_DIR"
    echo "💡 请先构建RimeKit.xcframework"
    exit 1
fi

# 集成到各个架构
for arch_dir in "$XCFRAMEWORK_DIR"/*; do
    if [ -d "$arch_dir" ] && [[ "$(basename "$arch_dir")" == "ios-arm64" ]]; then
        echo "📱 集成到架构: $(basename "$arch_dir")"
        
        # 创建Resources目录
        resources_dir="$arch_dir/Resources/RimeKitResources.bundle"
        mkdir -p "$resources_dir/prebuilt"
        
        # 复制预编译文件
        cp -r "$PREBUILT_DIR"/* "$resources_dir/prebuilt/"
        
        # 复制配置文件
        if [ -d "$SCRIPT_DIR/assets/rime-data" ]; then
            mkdir -p "$resources_dir/rime-data"
            cp -r "$SCRIPT_DIR/assets/rime-data"/* "$resources_dir/rime-data/"
        fi
        
        echo "  ✅ 集成完成"
    fi
done

echo ""
echo "🎉 iOS预编译文件集成完成！"
echo "💡 现在iOS应用可以使用预编译词典，显著提升启动速度"
EOF

chmod +x "$SCRIPT_DIR/integrate_prebuilt_ios.sh"

echo ""
echo "✅ iOS预编译完成！"
echo "📂 预编译文件位置: $PREBUILT_DIR"
echo "📱 iOS集成脚本: $SCRIPT_DIR/integrate_prebuilt_ios.sh"
echo ""
echo "🚀 下一步操作："
echo "   1. 运行集成脚本: ./integrate_prebuilt_ios.sh"
echo "   2. 重新构建RimeKit.xcframework"
echo "   3. 更新iOS应用中的框架引用"
echo ""
echo "💡 预编译文件可显著提升iOS应用的Rime初始化速度！"

# 清理构建目录（可选）
if [ "$1" != "--keep-build" ]; then
    echo "🧹 清理构建目录..."
    rm -rf "$BUILD_DIR"
fi

echo "🎉 脚本执行完成！"