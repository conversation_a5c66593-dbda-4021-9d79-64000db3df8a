// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		7945C38E2E2001550011A161 /* boost_regex.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C3802E2001550011A161 /* boost_regex.xcframework */; };
		7945C38F2E2001550011A161 /* icui18n.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C3832E2001550011A161 /* icui18n.xcframework */; };
		7945C3902E2001550011A161 /* icuuc.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C3852E2001550011A161 /* icuuc.xcframework */; };
		7945C3912E2001550011A161 /* boost_atomic.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C37D2E2001550011A161 /* boost_atomic.xcframework */; };
		7945C3922E2001550011A161 /* librime.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C38A2E2001550011A161 /* librime.xcframework */; };
		7945C3932E2001550011A161 /* icuio.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C3842E2001550011A161 /* icuio.xcframework */; };
		7945C3942E2001550011A161 /* boost_locale.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C37F2E2001550011A161 /* boost_locale.xcframework */; };
		7945C3952E2001550011A161 /* libleveldb.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C3872E2001550011A161 /* libleveldb.xcframework */; };
		7945C3962E2001550011A161 /* libopencc.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C3892E2001550011A161 /* libopencc.xcframework */; };
		7945C3972E2001550011A161 /* libglog.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C3862E2001550011A161 /* libglog.xcframework */; };
		7945C3982E2001550011A161 /* icudata.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C3822E2001550011A161 /* icudata.xcframework */; };
		7945C3992E2001550011A161 /* boost_filesystem.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C37E2E2001550011A161 /* boost_filesystem.xcframework */; };
		7945C39B2E2001550011A161 /* libmarisa.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C3882E2001550011A161 /* libmarisa.xcframework */; };
		7945C39C2E2001550011A161 /* boost_system.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C3812E2001550011A161 /* boost_system.xcframework */; };
		7945C39D2E2001550011A161 /* libyaml-cpp.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7945C38C2E2001550011A161 /* libyaml-cpp.xcframework */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		7945C36D2E20013E0011A161 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		7945C36F2E20013E0011A161 /* libRimeKit.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRimeKit.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7945C37C2E2001550011A161 /* .gitkeep */ = {isa = PBXFileReference; lastKnownFileType = text; path = .gitkeep; sourceTree = "<group>"; };
		7945C37D2E2001550011A161 /* boost_atomic.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = boost_atomic.xcframework; sourceTree = "<group>"; };
		7945C37E2E2001550011A161 /* boost_filesystem.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = boost_filesystem.xcframework; sourceTree = "<group>"; };
		7945C37F2E2001550011A161 /* boost_locale.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = boost_locale.xcframework; sourceTree = "<group>"; };
		7945C3802E2001550011A161 /* boost_regex.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = boost_regex.xcframework; sourceTree = "<group>"; };
		7945C3812E2001550011A161 /* boost_system.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = boost_system.xcframework; sourceTree = "<group>"; };
		7945C3822E2001550011A161 /* icudata.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = icudata.xcframework; sourceTree = "<group>"; };
		7945C3832E2001550011A161 /* icui18n.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = icui18n.xcframework; sourceTree = "<group>"; };
		7945C3842E2001550011A161 /* icuio.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = icuio.xcframework; sourceTree = "<group>"; };
		7945C3852E2001550011A161 /* icuuc.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = icuuc.xcframework; sourceTree = "<group>"; };
		7945C3862E2001550011A161 /* libglog.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = libglog.xcframework; sourceTree = "<group>"; };
		7945C3872E2001550011A161 /* libleveldb.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = libleveldb.xcframework; sourceTree = "<group>"; };
		7945C3882E2001550011A161 /* libmarisa.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = libmarisa.xcframework; sourceTree = "<group>"; };
		7945C3892E2001550011A161 /* libopencc.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = libopencc.xcframework; sourceTree = "<group>"; };
		7945C38A2E2001550011A161 /* librime.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = librime.xcframework; sourceTree = "<group>"; };
		7945C38C2E2001550011A161 /* libyaml-cpp.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = "libyaml-cpp.xcframework"; sourceTree = "<group>"; };
		79C44BD42E20AAB9009D1AC7 /* prebuild_info.yaml */ = {isa = PBXFileReference; lastKnownFileType = text.yaml; path = prebuild_info.yaml; sourceTree = "<group>"; };
		79C44BD52E20AAB9009D1AC7 /* rime_ice.prism.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = rime_ice.prism.bin; sourceTree = "<group>"; };
		79C44BD62E20AAB9009D1AC7 /* rime_ice.reverse.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = rime_ice.reverse.bin; sourceTree = "<group>"; };
		79C44BD72E20AAB9009D1AC7 /* rime_ice.table.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = rime_ice.table.bin; sourceTree = "<group>"; };
		79C44BD82E20AAB9009D1AC7 /* t9.prism.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = t9.prism.bin; sourceTree = "<group>"; };
		79C44BDA2E20AAB9009D1AC7 /* default.yaml */ = {isa = PBXFileReference; lastKnownFileType = text.yaml; path = default.yaml; sourceTree = "<group>"; };
		79C44BDB2E20AAB9009D1AC7 /* rime_ice.dict.yaml */ = {isa = PBXFileReference; lastKnownFileType = text.yaml; path = rime_ice.dict.yaml; sourceTree = "<group>"; };
		79C44BDC2E20AAB9009D1AC7 /* rime_ice.schema.yaml */ = {isa = PBXFileReference; lastKnownFileType = text.yaml; path = rime_ice.schema.yaml; sourceTree = "<group>"; };
		79C44BDD2E20AAB9009D1AC7 /* symbols_v.yaml */ = {isa = PBXFileReference; lastKnownFileType = text.yaml; path = symbols_v.yaml; sourceTree = "<group>"; };
		79C44BDE2E20AAB9009D1AC7 /* t9.schema.yaml */ = {isa = PBXFileReference; lastKnownFileType = text.yaml; path = t9.schema.yaml; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */
		7945C3762E20013E0011A161 /* Exceptions for "RimeKit" folder in "Copy Files" phase from "RimeKit" target */ = {
			isa = PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet;
			buildPhase = 7945C36D2E20013E0011A161 /* CopyFiles */;
			membershipExceptions = (
				include/rime_api_deprecated.h,
				include/rime_api_impl.h,
				include/rime_api_stdbool.h,
				include/rime_api.h,
				include/rime_levers_api.h,
				RimeCandidate.h,
				RimeCandidateList.h,
				RimeCommit.h,
				RimeComposition.h,
				RimeContext.h,
				RimeEngine.h,
				RimeInputManager.h,
				RimeInputSession.h,
				RimeKeyboardEvent.h,
				RimeKit.h,
				RimeKitResourceLoader.h,
				RimeManager.h,
				RimeMenu.h,
				RimeSchemaList.h,
				RimeStatus.h,
				RimeTraits.h,
				RimeTypes.h,
			);
		};
/* End PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7945C3712E20013E0011A161 /* RimeKit */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				7945C3762E20013E0011A161 /* Exceptions for "RimeKit" folder in "Copy Files" phase from "RimeKit" target */,
			);
			path = RimeKit;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7945C36C2E20013E0011A161 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7945C38E2E2001550011A161 /* boost_regex.xcframework in Frameworks */,
				7945C38F2E2001550011A161 /* icui18n.xcframework in Frameworks */,
				7945C3902E2001550011A161 /* icuuc.xcframework in Frameworks */,
				7945C3912E2001550011A161 /* boost_atomic.xcframework in Frameworks */,
				7945C3922E2001550011A161 /* librime.xcframework in Frameworks */,
				7945C3932E2001550011A161 /* icuio.xcframework in Frameworks */,
				7945C3942E2001550011A161 /* boost_locale.xcframework in Frameworks */,
				7945C3952E2001550011A161 /* libleveldb.xcframework in Frameworks */,
				7945C3962E2001550011A161 /* libopencc.xcframework in Frameworks */,
				7945C3972E2001550011A161 /* libglog.xcframework in Frameworks */,
				7945C3982E2001550011A161 /* icudata.xcframework in Frameworks */,
				7945C3992E2001550011A161 /* boost_filesystem.xcframework in Frameworks */,
				7945C39B2E2001550011A161 /* libmarisa.xcframework in Frameworks */,
				7945C39C2E2001550011A161 /* boost_system.xcframework in Frameworks */,
				7945C39D2E2001550011A161 /* libyaml-cpp.xcframework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7945C3662E20013E0011A161 = {
			isa = PBXGroup;
			children = (
				79C44BE02E20AAB9009D1AC7 /* assets */,
				7945C38D2E2001550011A161 /* Frameworks */,
				7945C3712E20013E0011A161 /* RimeKit */,
				7945C3702E20013E0011A161 /* Products */,
			);
			sourceTree = "<group>";
		};
		7945C3702E20013E0011A161 /* Products */ = {
			isa = PBXGroup;
			children = (
				7945C36F2E20013E0011A161 /* libRimeKit.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7945C38D2E2001550011A161 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				7945C37C2E2001550011A161 /* .gitkeep */,
				7945C37D2E2001550011A161 /* boost_atomic.xcframework */,
				7945C37E2E2001550011A161 /* boost_filesystem.xcframework */,
				7945C37F2E2001550011A161 /* boost_locale.xcframework */,
				7945C3802E2001550011A161 /* boost_regex.xcframework */,
				7945C3812E2001550011A161 /* boost_system.xcframework */,
				7945C3822E2001550011A161 /* icudata.xcframework */,
				7945C3832E2001550011A161 /* icui18n.xcframework */,
				7945C3842E2001550011A161 /* icuio.xcframework */,
				7945C3852E2001550011A161 /* icuuc.xcframework */,
				7945C3862E2001550011A161 /* libglog.xcframework */,
				7945C3872E2001550011A161 /* libleveldb.xcframework */,
				7945C3882E2001550011A161 /* libmarisa.xcframework */,
				7945C3892E2001550011A161 /* libopencc.xcframework */,
				7945C38A2E2001550011A161 /* librime.xcframework */,
				7945C38C2E2001550011A161 /* libyaml-cpp.xcframework */,
			);
			path = Frameworks;
			sourceTree = "<group>";
		};
		79C44BD92E20AAB9009D1AC7 /* prebuilt */ = {
			isa = PBXGroup;
			children = (
				79C44BD42E20AAB9009D1AC7 /* prebuild_info.yaml */,
				79C44BD52E20AAB9009D1AC7 /* rime_ice.prism.bin */,
				79C44BD62E20AAB9009D1AC7 /* rime_ice.reverse.bin */,
				79C44BD72E20AAB9009D1AC7 /* rime_ice.table.bin */,
				79C44BD82E20AAB9009D1AC7 /* t9.prism.bin */,
			);
			path = prebuilt;
			sourceTree = "<group>";
		};
		79C44BDF2E20AAB9009D1AC7 /* rime-data */ = {
			isa = PBXGroup;
			children = (
				79C44BDA2E20AAB9009D1AC7 /* default.yaml */,
				79C44BDB2E20AAB9009D1AC7 /* rime_ice.dict.yaml */,
				79C44BDC2E20AAB9009D1AC7 /* rime_ice.schema.yaml */,
				79C44BDD2E20AAB9009D1AC7 /* symbols_v.yaml */,
				79C44BDE2E20AAB9009D1AC7 /* t9.schema.yaml */,
			);
			path = "rime-data";
			sourceTree = "<group>";
		};
		79C44BE02E20AAB9009D1AC7 /* assets */ = {
			isa = PBXGroup;
			children = (
				79C44BD92E20AAB9009D1AC7 /* prebuilt */,
				79C44BDF2E20AAB9009D1AC7 /* rime-data */,
			);
			path = assets;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7945C36E2E20013E0011A161 /* RimeKit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7945C3792E20013E0011A161 /* Build configuration list for PBXNativeTarget "RimeKit" */;
			buildPhases = (
				7945C36B2E20013E0011A161 /* Sources */,
				7945C36C2E20013E0011A161 /* Frameworks */,
				7945C36D2E20013E0011A161 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7945C3712E20013E0011A161 /* RimeKit */,
			);
			name = RimeKit;
			packageProductDependencies = (
			);
			productName = RimeKit;
			productReference = 7945C36F2E20013E0011A161 /* libRimeKit.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7945C3672E20013E0011A161 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				KnownAssetTags = (
					New,
				);
				LastUpgradeCheck = 1610;
				TargetAttributes = {
					7945C36E2E20013E0011A161 = {
						CreatedOnToolsVersion = 16.1;
					};
				};
			};
			buildConfigurationList = 7945C36A2E20013E0011A161 /* Build configuration list for PBXProject "RimeKit" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7945C3662E20013E0011A161;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 7945C3702E20013E0011A161 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7945C36E2E20013E0011A161 /* RimeKit */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		7945C36B2E20013E0011A161 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		7945C3772E20013E0011A161 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		7945C3782E20013E0011A161 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7945C37A2E20013E0011A161 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = 3MSH7PD3J5;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7945C37B2E20013E0011A161 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = 3MSH7PD3J5;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7945C36A2E20013E0011A161 /* Build configuration list for PBXProject "RimeKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7945C3772E20013E0011A161 /* Debug */,
				7945C3782E20013E0011A161 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7945C3792E20013E0011A161 /* Build configuration list for PBXNativeTarget "RimeKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7945C37A2E20013E0011A161 /* Debug */,
				7945C37B2E20013E0011A161 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7945C3672E20013E0011A161 /* Project object */;
}
