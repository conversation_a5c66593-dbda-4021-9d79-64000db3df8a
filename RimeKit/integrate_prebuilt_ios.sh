#!/bin/bash

# iOS预编译文件集成脚本
# 将预编译文件集成到RimeKit.xcframework中

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PREBUILT_DIR="$SCRIPT_DIR/assets/prebuilt"
XCFRAMEWORK_DIR="$SCRIPT_DIR/output/RimeKit.xcframework"

echo "🍎 集成预编译文件到iOS框架..."

# 检查必要目录
if [ ! -d "$PREBUILT_DIR" ]; then
    echo "❌ 预编译目录不存在: $PREBUILT_DIR"
    echo "💡 请先运行 ./prebuild_dictionaries_ios.sh"
    exit 1
fi

if [ ! -d "$XCFRAMEWORK_DIR" ]; then
    echo "❌ XCFramework不存在: $XCFRAMEWORK_DIR"
    echo "💡 请先构建RimeKit.xcframework"
    exit 1
fi

# 集成到各个架构
for arch_dir in "$XCFRAMEWORK_DIR"/*; do
    if [ -d "$arch_dir" ] && [[ "$(basename "$arch_dir")" == "ios-arm64" ]]; then
        echo "📱 集成到架构: $(basename "$arch_dir")"
        
        # 创建Resources目录
        resources_dir="$arch_dir/Resources/RimeKitResources.bundle"
        mkdir -p "$resources_dir/prebuilt"
        
        # 复制预编译文件
        cp -r "$PREBUILT_DIR"/* "$resources_dir/prebuilt/"
        
        # 复制配置文件
        if [ -d "$SCRIPT_DIR/assets/rime-data" ]; then
            mkdir -p "$resources_dir/rime-data"
            cp -r "$SCRIPT_DIR/assets/rime-data"/* "$resources_dir/rime-data/"
        fi
        
        echo "  ✅ 集成完成"
    fi
done

echo ""
echo "🎉 iOS预编译文件集成完成！"
echo "💡 现在iOS应用可以使用预编译词典，显著提升启动速度"
