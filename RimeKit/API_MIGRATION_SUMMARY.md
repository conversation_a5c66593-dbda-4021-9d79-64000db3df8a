# RimeKit API 迁移总结

## 概述

本次更新将RimeKit的`inputKey`方法从同步改为异步，参考Android端实现，提供更好的性能和用户体验。

## 主要变更

### 1. RimeInputSession API变更

**之前（同步）：**
```objc
- (nullable RimeCandidateList *)inputKey:(RimeKeyboardEvent *)keyEvent;
```

**现在（异步）：**
```objc
// 统一回调方式
- (void)inputKey:(RimeKeyboardEvent *)keyEvent callback:(RimeInputKeyCallback)callback;

// 分离回调方式
- (void)inputKey:(RimeKeyboardEvent *)keyEvent 
         success:(RimeInputKeySuccessCallback)successCallback
           error:(RimeInputKeyErrorCallback)errorCallback;
```

### 2. RimeManager API变更

**之前（同步）：**
```objc
- (nullable RimeCandidateList *)inputKey:(RimeKeyboardEvent *)keyEvent;
```

**现在（异步）：**
```objc
- (void)inputKey:(RimeKeyboardEvent *)keyEvent callback:(RimeManagerInputCallback)callback;
```

## 迁移指南

### 旧代码模式
```objc
RimeKeyboardEvent *event = [RimeKeyboardEvent fromCharacter:'a'];
RimeCandidateList *candidates = [session inputKey:event];
if (candidates) {
    // 处理候选词
    for (RimeCandidate *candidate in candidates.candidates) {
        NSLog(@"候选词: %@", candidate.text);
    }
}
```

### 新代码模式
```objc
RimeKeyboardEvent *event = [RimeKeyboardEvent fromCharacter:'a'];
[session inputKey:event callback:^(RimeCandidateList * _Nullable candidates, 
                                  RimeComposition * _Nullable composition, 
                                  NSString * _Nullable error) {
    if (error) {
        NSLog(@"处理失败: %@", error);
    } else if (candidates) {
        // 处理候选词
        for (RimeCandidate *candidate in candidates.candidates) {
            NSLog(@"候选词: %@", candidate.text);
        }
    }
}];
```

## 已更新的文件

### 核心文件
- `RimeKit/RimeInputSession.h` - 更新API声明
- `RimeKit/RimeInputSession.m` - 实现异步处理逻辑
- `RimeKit/RimeManager.h` - 更新API声明
- `RimeKit/RimeManager.m` - 适配异步API调用

### 示例文件
- `Example/RimeKitExample.m` - 添加异步处理演示
- `Example/SimpleRimeViewController.m` - 更新为异步API调用
- `RimeKit/RimeKit.h` - 更新使用指南

### 文档文件
- `ASYNC_INPUT_API.md` - 异步API使用指南
- `API_MIGRATION_SUMMARY.md` - 本迁移总结文档

## 技术实现细节

### 线程模型
- **处理线程**: 串行队列`com.rimekit.inputsession.processing`
- **回调线程**: 主线程（方便UI更新）
- **委托更新**: 自动在主线程中调用

### 错误处理
- 会话无效或已关闭
- 按键处理失败
- 引擎内部异常

### 性能优势
1. **非阻塞**: 不会阻塞主线程
2. **顺序保证**: 串行队列确保处理顺序
3. **内存安全**: 完善的异常处理
4. **向后兼容**: 保留其他同步API

## 注意事项

1. **回调执行**: 所有回调都在主线程中执行
2. **错误处理**: 必须检查error参数
3. **内存管理**: 注意回调块中的循环引用
4. **委托更新**: 异步处理完成后会自动更新delegate

## 兼容性

- **iOS版本**: 支持iOS 12.0+
- **Xcode版本**: 支持Xcode 12.0+
- **向后兼容**: 其他API保持不变

## 测试建议

1. 测试异步回调的正确性
2. 验证错误处理逻辑
3. 检查UI更新的流畅性
4. 确认内存泄漏问题

## 后续计划

1. 监控性能表现
2. 收集用户反馈
3. 考虑其他API的异步化
4. 完善文档和示例
